# """
# 智能康复系统 - 系统协调器
# 负责整体流程控制和组件间通信协调
# """
# import logging
# import time
# from typing import Dict, Any, Optional, Union
# from models.system_states import SystemState, StateTransitionEvent
# from models.data_models import SystemStateData, PoseDataConverter
# from .state_manager import state_manager
# from .state_handlers import state_handler_factory

# class SystemCoordinator:
#     """系统协调器"""
#     # - 整体流程控制
#     # - 数据流协调 (ZMQ → 状态机 → WebSocket)
#     # - 组件间通信
#     # - 异常处理和恢复
#     def __init__(self):
#         """初始化系统协调器"""
#         self.logger = logging.getLogger(__name__)
        
#         # 组件引用
#         self.state_manager = state_manager
#         self.handler_factory = state_handler_factory
#         # 状态上下文
#         self.context: Dict[str, Any] = {
#             "current_time": time.time(),
#             "session_active": False
#         }
        
#         # 前端连接状态
#         self.frontend_connected = False

#         # WebSocket管理器引用（延迟初始化）
#         self.websocket_manager = None
        
#         # 注册状态变化回调
#         self._register_state_callbacks()
        
#         self.logger.info("系统协调器初始化完成")
    
#     def set_websocket_manager(self, websocket_manager):
#         """设置WebSocket管理器引用"""
#         self.websocket_manager = websocket_manager
#         self.logger.info("WebSocket管理器已连接到系统协调器")

#     def handle_frontend_disconnect(self):
#         """处理前端断开连接"""
#         self.frontend_connected = False
#         self.logger.info("前端连接断开，系统进入待机状态")
#         # 重置状态机到IDLE
#         self.state_manager.reset_to_idle()
#         self.reset_system()
#     def set_frontend_connection_status(self, connected: bool):
#         """设置前端连接状态"""
#         self.frontend_connected = connected
#         if connected:
#             self.logger.info("前端连接已建立，系统准备就绪")
#         else:
#             self.logger.info("前端连接断开，系统进入待机状态")

#     def handle_system_reset_request(self, data):
#         """处理系统重置请求"""
#         try:
#             self.logger.info(f"收到系统重置请求: {data}")
#             self.reset_system()
#             self.logger.info("系统重置请求处理完成")
#         except Exception as e:
#             self.logger.error(f"处理系统重置请求失败: {e}")
#     def _register_state_callbacks(self):
#         """注册状态变化回调"""
#         # 为每个状态注册回调
#         for state in SystemState:
#             self.state_manager.register_state_callback(
#                 state, 
#                 self._on_state_changed
#             )
    
#     def _on_state_changed(self, new_state: SystemState, old_state: SystemState, context):
#         """状态变化回调 - 只负责状态初始化，不发送WebSocket消息"""
#         try:
#             self.logger.info(f"状态变化: {old_state.value} → {new_state.value}")
#             # 获取新状态的处理器并执行状态初始化
#             handler = self.handler_factory.get_handler(new_state)
#             if handler:
#                 result = handler.enter_state(self.context)
#                 if result:
#                    self.logger.debug(f"状态初始化完成: {result.get('message', '无消息')}")
#             else:
#                 self.logger.warning(f"未找到状态处理器: {new_state.value}")
#         except Exception as e:
#             self.logger.error(f"状态变化处理失败: {e}")
    
#     def handle_zmq_data(self, camera_frame , detect_data):
#         """统一处理ZMQ数据 - 同时接收camera和pose数据"""
#         if not self.frontend_connected:
#             self.logger.debug("前端未连接，忽略姿态数据")
#             return
#         try:
#             # 更新上下文时间
#             self.context["current_time"] = time.time()
#             # 获取当前状态处理器
#             current_state = self.state_manager.get_current_state()
#             handler = self.handler_factory.get_handler(current_state)
#             if not handler:
#                 self.logger.warning(f"未找到状态处理器: {current_state.value}")
#                 return
#             # 数据类型转换：确保传递给handler的是ZMQDetectData对象
            

#             # 处理数据
#             result = handler.handle_data(detect_data, self.context)
#             print(result)
#             # 先发送处理结果（如USER_DETECTED消息）
#             state_data = result.get("state_data")
#             if state_data != None:
#                 result.get("state_data").detect_data = detect_data.pose_keypoints
#                 result.get("state_data").frame_data = camera_frame["frame_data"]
#             else:
#                 result["state_data"] = SystemStateData(
#                     current_state=current_state,
#                     pose_keypoints=detect_data.pose_keypoints,
#                     frame_data=camera_frame["frame_data"]
#                 )
#             self._send_data_result("zmq_data", result)
#             # 然后检查是否需要状态转换
#             self._check_state_transition(result)
#         except Exception as e:
#             self.logger.error(f"处理姿态数据失败: {e}")

#     def _check_state_transition(self, result: Dict[str, Any]):
#         """检查并执行状态转换"""
#         try:
#             trigger_event = result.get("trigger_event")
#             if not trigger_event:
#                 return
#             # 执行状态转换
#             success = self.state_manager.transition_to(trigger_event, **result)
#             if not success:
#                 self.logger.warning(f"状态转换失败: {trigger_event}")
                
#         except Exception as e:
#             self.logger.error(f"状态转换检查失败: {e}")
    
#     def _send_data_result(self, data_type: Any, result: Dict[str, Any]):
#         """发送数据处理结果"""
#         try:
#             if self.websocket_manager and result.get("success"):
#                 # 检查是否需要发送特定的WebSocket消息
#                 websocket_message = result.get("websocket_message")
#                 state_data = result.get("state_data")
#                 if websocket_message and state_data:
#                     # 使用WebSocket管理器发送状态消息
#                     self.websocket_manager.send_state_message(
#                         websocket_message,
#                         state_data,
#                     )
#                     # 处理消息类型（可能是枚举或字符串）
#                     msg_type = websocket_message.value if hasattr(websocket_message, 'value') else str(websocket_message)
#                     self.logger.info(f"广播状态消息: {msg_type} (来源: {data_type})")
#                 else:
#                     self.logger.debug(f"数据处理结果无需发送WebSocket消息: {data_type}")
#         except Exception as e:
#             self.logger.error(f"广播数据结果失败: {e}")

#     def get_system_status(self) -> Dict[str, Any]:
#         """获取系统状态"""
#         try:
#             current_state = self.state_manager.get_current_state()
#             state_duration = self.state_manager.get_state_duration()
#             return {
#                 "current_state": current_state.value,
#                 "state_duration": state_duration,
#                 "session_active": self.context.get("session_active", False),
#                 "user_info": self.context.get("user_info"),
#                 "current_action": self.context.get("current_action"),
#                 "context_keys": list(self.context.keys()),
#                 "websocket_connected": self.websocket_manager is not None
#             }
            
#         except Exception as e:
#             self.logger.error(f"获取系统状态失败: {e}")
#             return {"error": str(e)}
    
#     def reset_system(self):
#         """重置系统"""
#         try:
#             self.logger.info("重置系统状态")
#             # 重置状态机
#             self.state_manager.reset_to_idle()
#             # 清理上下文
#             self.context.clear()
#             self.context.update({
#                 "current_time": time.time(),
#                 "session_active": False
#             })
#             # 重置任务加载器
#             from .task.task_loader import task_loader
#             task_loader.reset_actions()
    
#             self.logger.info("系统重置完成")
            
#         except Exception as e:
#             self.logger.error(f"系统重置失败: {e}")
    
#     def start_session(self):
#         """开始会话"""
#         self.context["session_active"] = True
#         self.context["session_start_time"] = time.time()
#         self.logger.info("训练会话开始")
    
#     def end_session(self):
#         """结束会话"""
#         session_duration = time.time() - self.context.get("session_start_time", 0)
#         self.context["session_active"] = False
#         self.context["session_duration"] = session_duration
#         self.logger.info(f"训练会话结束，持续时间: {session_duration:.1f}秒")

# # 全局系统协调器实例
# system_coordinator = SystemCoordinator()
