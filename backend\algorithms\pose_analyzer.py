"""
智能康复系统 - 姿态分析服务
实现RTMPose关键点分析和姿态数据处理
"""
import numpy as np
import logging
import time
from typing import List, Dict, Optional, Tuple, Any
from models.data_models import ZMQDetectData
from models.constants import KeyPointMapping, ActionKeyPoints

class PoseAnalyzer:
    """姿态分析器"""
    
    def __init__(self):
        """初始化姿态分析器"""
        self.logger = logging.getLogger(__name__)
        
        # 关键点历史记录（用于平滑处理）
        self.keypoint_history: List[List] = []
        self.history_size = 5  # 保留最近5帧
        
        # 姿态质量评估参数
        self.confidence_threshold = 0.3  # 关键点置信度阈值
        self.stability_threshold = 10.0  # 稳定性阈值（像素）
        
        # 统计信息
        self.stats = {
            'total_frames': 0,
            'valid_frames': 0,
            'invalid_frames': 0,
            'average_confidence': 0.0,
            'processing_time': 0.0
        }
        
        self.logger.info("姿态分析器初始化完成")
    
    def analyze_pose(self, pose_data: ZMQDetectData) -> Dict[str, Any]:
        """
        分析姿态数据
        
        Args:
            pose_data: 姿态数据
            
        Returns:
            Dict: 分析结果
        """
        start_time = time.time()
        
        try:
            self.stats['total_frames'] += 1
        
            
            # 提取关键点
            keypoints = pose_data.pose_keypoints
            if not keypoints or len(keypoints) != 133:
                self.stats['invalid_frames'] += 1
                return self._create_invalid_result("关键点数量不正确")
            
            # 计算平均置信度
            avg_confidence = self._calculate_average_confidence(keypoints)
            if avg_confidence < self.confidence_threshold:
                self.stats['invalid_frames'] += 1
                return self._create_invalid_result("关键点置信度过低")
            
            # 更新历史记录
            self._update_history(keypoints)
            
            # 平滑处理
            smoothed_keypoints = self._smooth_keypoints(keypoints)
            
            # 姿态质量评估
            quality_score = self._assess_pose_quality(smoothed_keypoints)
            
            # 身体部位分析
            body_parts = self._analyze_body_parts(smoothed_keypoints)
            
            # 姿态稳定性分析
            stability_score = self._analyze_stability()
            
            self.stats['valid_frames'] += 1
            self.stats['average_confidence'] = (
                (self.stats['average_confidence'] * (self.stats['valid_frames'] - 1) + avg_confidence) 
                / self.stats['valid_frames']
            )
            
            processing_time = time.time() - start_time
            self.stats['processing_time'] = processing_time
            
            return {
                'valid': True,
                'keypoints': [kp.__dict__ for kp in smoothed_keypoints],
                'confidence': avg_confidence,
                'quality_score': quality_score,
                'stability_score': stability_score,
                'body_parts': body_parts,
                'processing_time': processing_time,
                'timestamp': pose_data.timestamp
            }
            
        except Exception as e:
            self.logger.error(f"姿态分析失败: {e}")
            self.stats['invalid_frames'] += 1
            return self._create_invalid_result(f"分析异常: {str(e)}")
    
    def _create_invalid_result(self, reason: str) -> Dict[str, Any]:
        """
        创建无效结果
        
        Args:
            reason: 无效原因
            
        Returns:
            Dict: 无效结果
        """
        return {
            'valid': False,
            'reason': reason,
            'keypoints': [],
            'confidence': 0.0,
            'quality_score': 0.0,
            'stability_score': 0.0,
            'body_parts': {},
            'processing_time': 0.0,
            'timestamp': time.time()
        }
    
    def _calculate_average_confidence(self, keypoints: List[List]) -> float:
        """
        计算平均置信度
        
        Args:
            keypoints: 关键点列表
            
        Returns:
            float: 平均置信度
        """
        if not keypoints:
            return 0.0
        
        total_confidence = sum(kp[2] for kp in keypoints)
        return total_confidence / len(keypoints)
    
    def _update_history(self, keypoints: List[List]):
        """
        更新关键点历史记录
        
        Args:
            keypoints: 关键点列表
        """
        self.keypoint_history.append(keypoints.copy())
        
        # 保持历史记录大小
        if len(self.keypoint_history) > self.history_size:
            self.keypoint_history.pop(0)
    
    def _smooth_keypoints(self, keypoints: List[List]) -> List[List]:
        """
        平滑关键点（使用移动平均）
        
        Args:
            keypoints: 当前关键点
            
        Returns:
            List[KeyPoint]: 平滑后的关键点
        """
        if len(self.keypoint_history) < 2:
            return keypoints
        
        smoothed_keypoints = []
        
        for i in range(len(keypoints)):
            # 收集历史位置
            x_values = []
            y_values = []
            confidences = []
            
            for frame_keypoints in self.keypoint_history:
                if i < len(frame_keypoints):
                    x_values.append(frame_keypoints[i].x)
                    y_values.append(frame_keypoints[i].y)
                    confidences.append(frame_keypoints[i].confidence)
            
            # 计算平均值
            if x_values and y_values and confidences:
                smoothed_x = sum(x_values) / len(x_values)
                smoothed_y = sum(y_values) / len(y_values)
                smoothed_confidence = sum(confidences) / len(confidences)
                
                smoothed_keypoints.append(list(
                    [smoothed_x, smoothed_y, smoothed_confidence]
                ))
            else:
                smoothed_keypoints.append(keypoints[i])
        
        return smoothed_keypoints
    
    def _assess_pose_quality(self, keypoints: List[List]) -> float:
        """
        评估姿态质量
        
        Args:
            keypoints: 关键点列表
            
        Returns:
            float: 质量分数 (0-100)
        """
        try:
            # 关键身体部位的关键点索引
            critical_points = [
                KeyPointMapping.NOSE,
                KeyPointMapping.LEFT_SHOULDER,
                KeyPointMapping.RIGHT_SHOULDER,
                KeyPointMapping.LEFT_ELBOW,
                KeyPointMapping.RIGHT_ELBOW,
                KeyPointMapping.LEFT_WRIST,
                KeyPointMapping.RIGHT_WRIST
            ]
            
            # 计算关键点的平均置信度
            critical_confidences = []
            for idx in critical_points:
                if idx < len(keypoints):
                    critical_confidences.append(keypoints[idx][2])
            
            if not critical_confidences:
                return 0.0
            
            avg_confidence = sum(critical_confidences) / len(critical_confidences)
            
            # 转换为0-100分数
            quality_score = min(100.0, avg_confidence * 100)
            
            return quality_score
            
        except Exception as e:
            self.logger.error(f"姿态质量评估失败: {e}")
            return 0.0
    
    def _analyze_body_parts(self, keypoints: List[List]) -> Dict[str, Any]:
        """
        分析身体部位
        
        Args:
            keypoints: 关键点列表
            
        Returns:
            Dict: 身体部位分析结果
        """
        try:
            body_parts = {}
            
            # 头部分析
            if KeyPointMapping.NOSE < len(keypoints):
                nose = keypoints[KeyPointMapping.NOSE]
                body_parts['head'] = {
                    'visible': nose[2] > self.confidence_threshold,
                    'confidence': nose[2],
                    'position': [ nose[0], nose[1]]
                }
            
            # 左臂分析
            left_arm_points = [
                KeyPointMapping.LEFT_SHOULDER,
                KeyPointMapping.LEFT_ELBOW,
                KeyPointMapping.LEFT_WRIST
            ]
            body_parts['left_arm'] = self._analyze_limb(keypoints, left_arm_points)
            
            # 右臂分析
            right_arm_points = [
                KeyPointMapping.RIGHT_SHOULDER,
                KeyPointMapping.RIGHT_ELBOW,
                KeyPointMapping.RIGHT_WRIST
            ]
            body_parts['right_arm'] = self._analyze_limb(keypoints, right_arm_points)
            
            # 手部分析（如果有手部关键点）
            body_parts['left_hand'] = self._analyze_hand(keypoints, 'left')
            body_parts['right_hand'] = self._analyze_hand(keypoints, 'right')
            
            return body_parts
            
        except Exception as e:
            self.logger.error(f"身体部位分析失败: {e}")
            return {}
    
    def _analyze_limb(self, keypoints: List[List], limb_points: List[int]) -> Dict[str, Any]:
        """
        分析肢体
        
        Args:
            keypoints: 关键点列表
            limb_points: 肢体关键点索引
            
        Returns:
            Dict: 肢体分析结果
        """
        try:
            visible_points = 0
            total_confidence = 0.0
            positions = []
            
            for idx in limb_points:
                if idx < len(keypoints):
                    kp = keypoints[idx]
                    if kp[2] > self.confidence_threshold:
                        visible_points += 1
                        total_confidence += kp[2]
                        positions.append([kp[0], kp[1]])
                    else:
                        positions.append(None)
                else:
                    positions.append(None)
            
            avg_confidence = total_confidence / len(limb_points) if limb_points else 0.0
            visibility_ratio = visible_points / len(limb_points) if limb_points else 0.0
            
            return {
                'visible': visibility_ratio > 0.5,
                'visibility_ratio': visibility_ratio,
                'confidence': avg_confidence,
                'positions': positions
            }
            
        except Exception as e:
            self.logger.error(f"肢体分析失败: {e}")
            return {'visible': False, 'visibility_ratio': 0.0, 'confidence': 0.0, 'positions': []}
    
    def _analyze_hand(self, keypoints: List[List], side: str) -> Dict[str, Any]:
        """
        分析手部
        
        Args:
            keypoints: 关键点列表
            side: 左右侧 ('left' 或 'right')
            
        Returns:
            Dict: 手部分析结果
        """
        try:
            # 手部关键点范围（RTMPose中手部关键点从21开始）
            if side == 'left':
                hand_start = 91  # 左手起始索引
            else:
                hand_start = 112  # 右手起始索引
            
            hand_end = hand_start + 21  # 每只手21个关键点
            
            visible_points = 0
            total_confidence = 0.0
            
            for idx in range(hand_start, min(hand_end, len(keypoints))):
                kp = keypoints[idx]
                if kp[2] > self.confidence_threshold:
                    visible_points += 1
                    total_confidence += kp[2]
            
            hand_points_count = min(21, len(keypoints) - hand_start)
            avg_confidence = total_confidence / hand_points_count if hand_points_count > 0 else 0.0
            visibility_ratio = visible_points / hand_points_count if hand_points_count > 0 else 0.0
            
            return {
                'visible': visibility_ratio > 0.3,  # 手部30%关键点可见即认为可见
                'visibility_ratio': visibility_ratio,
                'confidence': avg_confidence,
                'points_count': hand_points_count
            }
            
        except Exception as e:
            self.logger.error(f"手部分析失败: {e}")
            return {'visible': False, 'visibility_ratio': 0.0, 'confidence': 0.0, 'points_count': 0}
    
    def _analyze_stability(self) -> float:
        """
        分析姿态稳定性
        
        Returns:
            float: 稳定性分数 (0-100)
        """
        try:
            if len(self.keypoint_history) < 2:
                return 100.0  # 历史数据不足，认为稳定
            
            # 计算关键点的移动距离
            total_movement = 0.0
            point_count = 0
            
            current_frame = self.keypoint_history[-1]
            previous_frame = self.keypoint_history[-2]
            
            for i in range(min(len(current_frame), len(previous_frame))):
                curr_kp = current_frame[i]
                prev_kp = previous_frame[i]
                
                # 只考虑置信度较高的关键点
                if curr_kp.confidence > self.confidence_threshold and prev_kp.confidence > self.confidence_threshold:
                    distance = np.sqrt((curr_kp.x - prev_kp.x)**2 + (curr_kp.y - prev_kp.y)**2)
                    total_movement += distance
                    point_count += 1
            
            if point_count == 0:
                return 0.0
            
            avg_movement = total_movement / point_count
            
            # 将移动距离转换为稳定性分数
            stability_score = max(0.0, 100.0 - (avg_movement / self.stability_threshold) * 100.0)
            
            return min(100.0, stability_score)
            
        except Exception as e:
            self.logger.error(f"稳定性分析失败: {e}")
            return 0.0
    
    def get_keypoint_by_index(self, keypoints: List[List], index: int) -> Optional[List]:
        """
        根据索引获取关键点
        
        Args:
            keypoints: 关键点列表
            index: 关键点索引
            
        Returns:
            KeyPoint: 关键点，不存在返回None
        """
        if 0 <= index < len(keypoints):
            return keypoints[index]
        return None
    
    def calculate_distance(self, kp1: List, kp2: List) -> float:
        """
        计算两个关键点之间的距离
        
        Args:
            kp1: 关键点1
            kp2: 关键点2
            
        Returns:
            float: 距离
        """
        return np.sqrt((kp1[0] - kp2[0])**2 + (kp1[1] - kp2[1])**2)
    
    def calculate_angle(self, kp1: List, kp2: List, kp3: List) -> float:
        """
        计算三个关键点形成的角度
        
        Args:
            kp1: 关键点1
            kp2: 关键点2（顶点）
            kp3: 关键点3
            
        Returns:
            float: 角度（度）
        """
        try:
            # 计算向量
            v1 = np.array([kp1[0] - kp2[0], kp1[1] - kp2[1]])
            v2 = np.array([kp3[0] - kp2[0], kp3[1] - kp2[1]])
            
            # 计算角度
            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            cos_angle = np.clip(cos_angle, -1.0, 1.0)  # 防止数值误差
            
            angle = np.arccos(cos_angle)
            return np.degrees(angle)
            
        except Exception as e:
            self.logger.error(f"角度计算失败: {e}")
            return 0.0
    
    def reset_history(self):
        """重置历史记录"""
        self.keypoint_history.clear()
        self.logger.info("姿态分析历史记录已重置")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict: 统计信息
        """
        return {
            **self.stats,
            'history_size': len(self.keypoint_history),
            'success_rate': (self.stats['valid_frames'] / self.stats['total_frames'] * 100) 
                           if self.stats['total_frames'] > 0 else 0.0
        }

# 全局姿态分析器实例
pose_analyzer = PoseAnalyzer()
