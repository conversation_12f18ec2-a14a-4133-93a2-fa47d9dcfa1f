/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        },
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          500: '#22c55e',
          600: '#16a34a',
        },
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          500: '#f59e0b',
          600: '#d97706',
        },
        danger: {
          50: '#fef2f2',
          100: '#fee2e2',
          500: '#ef4444',
          600: '#dc2626',
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      height: {
        'screen-90': '90vh',
        'screen-80': '80vh',
      },
      backgroundImage: {
        'gradient-conic': 'conic-gradient(var(--tw-gradient-stops))',
        'gradient-conic-from-green': 'conic-gradient(from 0deg, #10b981 0deg, #10b981 324deg, #e5e7eb 324deg)',
        'gradient-conic-from-yellow': 'conic-gradient(from 0deg, #f59e0b 0deg, #f59e0b 270deg, #e5e7eb 270deg)',
        'gradient-conic-from-orange': 'conic-gradient(from 0deg, #f97316 0deg, #f97316 180deg, #e5e7eb 180deg)',
        'gradient-conic-from-red': 'conic-gradient(from 0deg, #dc2626 0deg, #dc2626 90deg, #e5e7eb 90deg)',
      }
    },
  },
  plugins: [],
}
