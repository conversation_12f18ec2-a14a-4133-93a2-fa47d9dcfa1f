@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Inter', system-ui, sans-serif;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 工具类 */
.flex-center {
  @apply flex items-center justify-center;
}

.text-gradient {
  @apply bg-gradient-to-r from-primary-500 to-primary-700 bg-clip-text text-transparent;
}

.card-shadow {
  @apply shadow-lg shadow-gray-200/50;
}

.transition-smooth {
  @apply transition-all duration-300 ease-in-out;
}
