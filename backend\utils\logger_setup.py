"""
智能康复系统 - 日志设置工具
提供统一的日志配置功能
"""
import os
import logging
import logging.handlers
from typing import Dict, Any, Optional
from pathlib import Path

class LoggerSetup:
    """日志设置器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化日志设置器"""
        self.config = config or self._get_default_config()
        self.loggers = {}
        
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认日志配置"""
        return {
            'level': 'INFO',
            'file_enabled': True,
            'console_enabled': True,
            'log_dir': 'logs',
            'max_file_size': '10MB',
            'backup_count': 5,
            'log_format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'date_format': '%Y-%m-%d %H:%M:%S'
        }
    
    def setup_logging(self, log_dir: Optional[str] = None):
        """设置全局日志配置"""
        try:
            # 确定日志目录
            if log_dir:
                self.config['log_dir'] = log_dir
            
            log_directory = Path(self.config['log_dir'])
            log_directory.mkdir(exist_ok=True)
            
            # 清除现有的处理器
            root_logger = logging.getLogger()
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 设置日志级别
            log_level = getattr(logging, self.config['level'].upper())
            root_logger.setLevel(log_level)
            
            # 创建格式器
            formatter = logging.Formatter(
                self.config['log_format'],
                datefmt=self.config['date_format']
            )
            
            # 添加控制台处理器
            if self.config['console_enabled']:
                console_handler = logging.StreamHandler()
                console_handler.setLevel(log_level)
                console_handler.setFormatter(formatter)
                root_logger.addHandler(console_handler)
            
            # 添加文件处理器
            if self.config['file_enabled']:
                # 主日志文件
                main_log_file = log_directory / 'system.log'
                file_handler = logging.handlers.RotatingFileHandler(
                    main_log_file,
                    maxBytes=self._parse_size(self.config['max_file_size']),
                    backupCount=self.config['backup_count'],
                    encoding='utf-8'
                )
                file_handler.setLevel(log_level)
                file_handler.setFormatter(formatter)
                root_logger.addHandler(file_handler)
                
                # 错误日志文件
                error_log_file = log_directory / 'error.log'
                error_handler = logging.handlers.RotatingFileHandler(
                    error_log_file,
                    maxBytes=self._parse_size(self.config['max_file_size']),
                    backupCount=self.config['backup_count'],
                    encoding='utf-8'
                )
                error_handler.setLevel(logging.ERROR)
                error_handler.setFormatter(formatter)
                root_logger.addHandler(error_handler)
            
            # 记录日志设置完成
            logging.info(f"日志系统初始化完成 - 级别: {self.config['level']}")
            
        except Exception as e:
            print(f"日志设置失败: {e}")
            raise
    
    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串"""
        size_str = size_str.upper()
        
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def create_module_logger(self, module_name: str, log_file: Optional[str] = None) -> logging.Logger:
        """为特定模块创建日志器"""
        try:
            logger = logging.getLogger(module_name)
            
            # 如果指定了日志文件，添加专用文件处理器
            if log_file and self.config['file_enabled']:
                log_directory = Path(self.config['log_dir'])
                module_log_file = log_directory / log_file
                
                # 创建文件处理器
                file_handler = logging.handlers.RotatingFileHandler(
                    module_log_file,
                    maxBytes=self._parse_size(self.config['max_file_size']),
                    backupCount=self.config['backup_count'],
                    encoding='utf-8'
                )
                
                # 设置格式器
                formatter = logging.Formatter(
                    self.config['log_format'],
                    datefmt=self.config['date_format']
                )
                file_handler.setFormatter(formatter)
                
                # 添加到日志器
                logger.addHandler(file_handler)
            
            self.loggers[module_name] = logger
            return logger
            
        except Exception as e:
            logging.error(f"创建模块日志器失败: {e}")
            return logging.getLogger(module_name)
    
    def set_log_level(self, level: str, logger_name: Optional[str] = None):
        """设置日志级别"""
        try:
            log_level = getattr(logging, level.upper())
            
            if logger_name:
                logger = logging.getLogger(logger_name)
                logger.setLevel(log_level)
            else:
                logging.getLogger().setLevel(log_level)
            
            logging.info(f"日志级别已设置为: {level}")
            
        except Exception as e:
            logging.error(f"设置日志级别失败: {e}")
    
    def add_file_handler(self, logger_name: str, file_path: str, level: str = 'INFO'):
        """为指定日志器添加文件处理器"""
        try:
            logger = logging.getLogger(logger_name)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 创建文件处理器
            file_handler = logging.handlers.RotatingFileHandler(
                file_path,
                maxBytes=self._parse_size(self.config['max_file_size']),
                backupCount=self.config['backup_count'],
                encoding='utf-8'
            )
            
            # 设置级别和格式
            file_handler.setLevel(getattr(logging, level.upper()))
            formatter = logging.Formatter(
                self.config['log_format'],
                datefmt=self.config['date_format']
            )
            file_handler.setFormatter(formatter)
            
            # 添加到日志器
            logger.addHandler(file_handler)
            
            logging.info(f"文件处理器已添加: {file_path}")
            
        except Exception as e:
            logging.error(f"添加文件处理器失败: {e}")
    
    def cleanup_old_logs(self, days: int = 30):
        """清理旧日志文件"""
        try:
            import time
            from datetime import datetime, timedelta
            
            log_directory = Path(self.config['log_dir'])
            if not log_directory.exists():
                return
            
            cutoff_time = time.time() - (days * 24 * 60 * 60)
            
            for log_file in log_directory.glob('*.log*'):
                if log_file.stat().st_mtime < cutoff_time:
                    log_file.unlink()
                    logging.info(f"已删除旧日志文件: {log_file}")
            
        except Exception as e:
            logging.error(f"清理旧日志失败: {e}")
    
    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        try:
            log_directory = Path(self.config['log_dir'])
            if not log_directory.exists():
                return {'error': '日志目录不存在'}
            
            stats = {
                'log_directory': str(log_directory),
                'total_files': 0,
                'total_size': 0,
                'files': []
            }
            
            for log_file in log_directory.glob('*.log*'):
                file_stat = log_file.stat()
                file_info = {
                    'name': log_file.name,
                    'size': file_stat.st_size,
                    'modified': datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                }
                
                stats['files'].append(file_info)
                stats['total_files'] += 1
                stats['total_size'] += file_stat.st_size
            
            return stats
            
        except Exception as e:
            return {'error': str(e)}

# 创建全局日志设置器
def setup_system_logging(config: Optional[Dict[str, Any]] = None) -> LoggerSetup:
    """设置系统日志"""
    logger_setup = LoggerSetup(config)
    logger_setup.setup_logging()
    return logger_setup
