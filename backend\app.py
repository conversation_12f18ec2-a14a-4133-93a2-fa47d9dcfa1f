"""
智能康复系统 - Flask应用程序
使用应用工厂模式，简化架构设计
"""
import logging
from flask import Flask
from flask_cors import CORS

# 导入扩展实例
from extensions import socketio, init_extensions

# 导入服务组件
from services.communication.websocket.websocket_manager import websocket_manager
from services.business.system_coordinator import system_coordinator
from services.communication.zmq.zmq_receiver import zmq_receiver

def create_app(config=None):
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 基础配置
    app.config.update({
        'SECRET_KEY': 'rehabilitation_system_secret_key',
        'DEBUG': False
    })
    
    # 更新自定义配置
    if config:
        app.config.update(config)
    
    # 初始化扩展
    init_extensions(app)
    
    # 注册路由
    _register_routes(app)
    
    # 初始化组件
    _initialize_components(app)
    
    return app

def _register_routes(app):
    """注册API路由"""
    @app.route('/health')
    def health_check():
        return {'status': 'healthy', 'message': '智能康复系统运行正常'}
    
    @app.route('/api/system/status')
    def get_system_status():
        """获取系统状态"""
        try:
            status = system_coordinator.get_system_status()
            return {'success': True, 'data': status}
        except Exception as e:
            return {'success': False, 'error': str(e)}, 500

def _initialize_components(app):
    """初始化系统组件"""
    logger = logging.getLogger(__name__)
    
    try:
        # 初始化WebSocket管理器
        websocket_manager.initialize(app, socketio)
        
        # 建立组件间连接
        websocket_manager.set_system_coordinator(system_coordinator)
        system_coordinator.set_websocket_manager(websocket_manager)
        
        # 设置ZMQ接收器的系统协调器引用
        zmq_receiver.set_system_coordinator(system_coordinator)
        
        # 设置ZMQ接收器的前端连接状态通知
        websocket_manager.zmq_receiver = zmq_receiver
        
        logger.info("系统组件初始化完成")
        logger.info(f"ZMQ接收器系统协调器状态: {zmq_receiver.system_coordinator is not None}")
        
    except Exception as e:
        logger.error(f"组件初始化失败: {e}")
        raise
# 创建应用实例
app = create_app()

if __name__ == '__main__':
    # 开发环境直接运行
    socketio.run(app, host='0.0.0.0', port=5000, debug=True)
