"""
智能康复系统 - REPORTING状态处理器
处理训练报告展示阶段
"""
import time
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent, MessageType, TimeConstants
from models.data_models import ZMQDetectData, SystemStateData
from . import BaseStateHandler
from ..user_detection_service import user_detection_service

class ReportingHandler(BaseStateHandler):
    """REPORTING状态处理器"""
    
    def __init__(self):
        """初始化REPORTING状态处理器"""
        super().__init__(SystemState.REPORTING)
        self.reporting_start_time = None
        self.user_lost_start_time = None
        self.last_user_id = None
    
    def enter_state(self, context: Dict[str, Any]):
        """进入REPORTING状态"""
        self.logger.info("系统进入训练报告展示状态")
    
    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理REPORTING状态下的数据"""
        try:
            # 检查用户状态（报告阶段也需要检测用户）
            user_status = self._check_user_status(data, context)
            
            # 检查是否需要暂停（虽然报告阶段暂停的可能性较小）
            if user_status["should_pause"]:
                return self._handle_pause(user_status, context)
            
            # 检查报告展示时间是否结束
            if self._is_reporting_finished():
                return self._finish_reporting(data, context)
            
            # 继续展示报告
            return self._send_reporting_data(data, context)
            

        except Exception as e:
            self.logger.error(f"处理报告状态数据失败: {e}")
            # 错误情况下也要发送状态数据
            error_state_data = SystemStateData(
                current_state=SystemState.REPORTING,
                message=f"数据处理失败: {str(e)}",
                user_info=context.get("user_info"),
                action_list=context.get("action_list", []),
                current_action=context.get("current_action"),
            )

            return {
                "success": False,
                "websocket_message": MessageType.ACTION_COMPLETED,
                "state_data": error_state_data,
                "message": f"数据处理失败: {str(e)}"
            }
    
    def _check_user_status(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """检查用户状态 - 使用公共用户检测服务"""
        # 首先检查用户是否在画面中
        presence_result = user_detection_service.check_user(pose_data, context)

        if presence_result.get("should_pause"):
            return {
                "should_pause": True,
                "pause_reason": presence_result.get("pause_reason"),
                "message": presence_result.get("message")
            }
        return {"should_pause": False}
    
    def _handle_pause(self, user_status: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理暂停逻辑"""
        pause_reason = user_status["pause_reason"]
        
        if pause_reason == "user_lost":
            event = StateTransitionEvent.USER_LOST
            message_type = MessageType.USER_LOST
        else:  # user_not_auth
            event = StateTransitionEvent.USER_NOT_AUTH
            message_type = MessageType.USER_NOT_AUTH
        
        # 创建暂停状态数据
        state_data = SystemStateData(
            current_state=SystemState.PAUSE,
            message=user_status["message"],
            user_info=context.get("user_info"),
            action_list=context.get("action_list", []),
            current_action=context.get("current_action"),
        )

        return {
            "success": True,
            "trigger_event": event,
            "next_state": SystemState.PAUSE,
            "websocket_message": message_type,
            "state_data": state_data,
            "message": user_status["message"]
        }
    
    def _is_reporting_finished(self) -> bool:
        """检查报告展示是否结束"""
        if self.reporting_start_time is None:
            return False
        return time.time() - self.reporting_start_time >= TimeConstants.REPORTING_TIMEOUT
    
    def _finish_reporting(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """结束报告阶段，返回等待状态"""
        self.logger.info("报告展示时间结束，返回等待状态")
        
        # 清除用户相关信息，准备下一个用户
        context.pop("user_info", None)
        context.pop("action_list", None)
        context.pop("current_action", None)
        context.pop("report_data", None)
        
        # 创建状态数据
        state_data = SystemStateData(
            current_state=SystemState.WAITING,
            message="训练完成，等待下一位用户",
        )
        
        return {
            "success": True,
            "trigger_event": StateTransitionEvent.CLOCK_REPORTING_FINISH,
            "next_state": SystemState.WAITING,
            "websocket_message": MessageType.CLOCK_REPORTING_FINISH,
            "state_data": state_data
        }
    
    def _send_reporting_data(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """发送报告状态数据"""
        # 计算剩余时间
        elapsed_time = time.time() - self.reporting_start_time if self.reporting_start_time else 0
        remaining_time = max(0, TimeConstants.REPORTING_TIMEOUT - elapsed_time)
        
        report_data = context.get("report_data", {})
        
        # 创建状态数据
        state_data = SystemStateData(
            current_state=SystemState.REPORTING,
            message=f"训练报告展示中... (剩余 {remaining_time:.0f} 秒)",
            user_info=context.get("user_info"),
            action_list=context.get("action_list", []),
            current_action=context.get("current_action"),
            progress_info=report_data  # 将报告数据放在progress_info中
        )
        
        return {
            "success": True,
            "websocket_message": MessageType.ACTION_COMPLETED,  # 持续发送报告状态
            "state_data": state_data,
            "remaining_time": remaining_time,
            "report_data": report_data
        }
    
    def _generate_report(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """生成训练报告数据"""
        user_info = context.get("user_info")
        action_list = context.get("action_list", [])
        current_action = context.get("current_action")
        
        # 计算训练统计
        total_actions = len(action_list)
        completed_actions = context.get("current_action_index", 0) + 1
        completion_rate = (completed_actions / total_actions * 100) if total_actions > 0 else 0
        
        # 获取最后一个动作的得分
        final_score = current_action.score if current_action and current_action.score else 0
        
        # 训练持续时间
        training_duration = time.time() - context.get("training_start_time", time.time())
        
        report_data = {
            "user_name": user_info.name if user_info else "未知用户",
            "training_date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_actions": total_actions,
            "completed_actions": completed_actions,
            "completion_rate": completion_rate,
            "final_score": final_score,
            "training_duration": training_duration,
            "performance_level": self._get_performance_level(final_score),
            "suggestions": self._get_suggestions(final_score, completion_rate)
        }
        
        self.logger.info(f"生成训练报告: 完成率 {completion_rate:.1f}%, 最终得分 {final_score:.1f}")
        
        return report_data
    
    def _get_performance_level(self, score: float) -> str:
        """根据得分获取表现等级"""
        if score >= 90:
            return "优秀"
        elif score >= 80:
            return "良好"
        elif score >= 70:
            return "一般"
        elif score >= 60:
            return "需要改进"
        else:
            return "需要加强练习"
    
    def _get_suggestions(self, score: float, completion_rate: float) -> list:
        """根据表现生成建议"""
        suggestions = []
        
        if completion_rate < 100:
            suggestions.append("建议完成所有训练动作以获得更好的康复效果")
        
        if score < 70:
            suggestions.append("动作准确性有待提高，建议多观看示范视频")
            suggestions.append("可以适当放慢动作速度，注重动作质量")
        elif score < 85:
            suggestions.append("动作完成得不错，继续保持")
            suggestions.append("可以尝试增加训练强度")
        else:
            suggestions.append("动作完成得非常好！")
            suggestions.append("可以考虑进行更高难度的训练")
        
        suggestions.append("建议每天坚持训练，保持康复进度")
        
        return suggestions
    
    def exit_state(self, context: Dict[str, Any]):
        """退出REPORTING状态"""
        self.logger.info("系统退出训练报告展示状态")
        self.reporting_start_time = None
        self.user_lost_start_time = None
