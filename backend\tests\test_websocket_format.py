"""
测试WebSocket消息格式统一性
验证前后端消息格式的一致性和session_id的正确处理
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import time
import uuid
from models.system_states import SystemState, MessageType
from models.data_models import WebSocketMessage, SystemStateData, DataSerializer
from backend.services.communication.websocket.websocket_manager import WebSocketHandler

class TestWebSocketFormat(unittest.TestCase):
    """测试WebSocket消息格式统一性"""
    
    def setUp(self):
        """设置测试环境"""
        self.websocket_handler = WebSocketHandler()
        
        # 模拟SocketIO
        self.mock_socketio = Mock()
        self.websocket_handler.socketio = self.mock_socketio
        
        # 模拟客户端连接
        self.client_id = "test_client_123"
        self.session_id = str(uuid.uuid4())
        
        # 设置连接信息
        self.websocket_handler.connected_clients[self.client_id] = {
            'connect_time': time.time(),
            'last_activity': time.time(),
            'session_id': self.session_id
        }
        self.websocket_handler.client_sessions[self.client_id] = self.session_id
        self.websocket_handler.stats['active_connections'] = 1
        
    def test_websocket_message_structure(self):
        """测试WebSocket消息结构"""
        # 创建测试消息
        test_data = {"test": "data", "value": 123}
        message = WebSocketMessage(
            message_type=MessageType.USER_DETECTED.value,
            session_id=self.session_id,
            timestamp=time.time(),
            data=test_data
        )
        
        # 序列化消息
        message_dict = DataSerializer.websocket_message_to_dict(message)
        
        # 验证消息结构
        self.assertIn('message_type', message_dict)
        self.assertIn('session_id', message_dict)
        self.assertIn('timestamp', message_dict)
        self.assertIn('data', message_dict)
        
        # 验证字段值
        self.assertEqual(message_dict['message_type'], MessageType.USER_DETECTED.value)
        self.assertEqual(message_dict['session_id'], self.session_id)
        self.assertEqual(message_dict['data'], test_data)
        
    def test_send_state_message_format(self):
        """测试发送状态消息格式"""
        from models.data_models import SystemStateData
        from models.system_states import SystemState

        # 创建SystemStateData
        state_data = SystemStateData(
            current_state=SystemState.USER_LOGIN,
            message="检测到用户",
            progress_info={
                "patient_id": "test_user_001",
                "detection_timestamp": time.time(),
                "confidence": 1.0,
                "detection_type": "pose_based"
            }
        )

        # 发送状态消息
        self.websocket_handler.send_state_message(MessageType.USER_DETECTED, state_data, self.client_id)

        # 验证emit被调用
        self.mock_socketio.emit.assert_called_once()

        # 获取调用参数
        call_args = self.mock_socketio.emit.call_args
        event_name = call_args[0][0]
        message_data = call_args[0][1]

        # 验证事件名称
        self.assertEqual(event_name, MessageType.USER_DETECTED.value)

        # 验证消息格式
        self.assertIn('message_type', message_data)
        self.assertIn('session_id', message_data)
        self.assertIn('timestamp', message_data)
        self.assertIn('data', message_data)

        # 验证session_id正确
        self.assertEqual(message_data['session_id'], self.session_id)

        # 验证数据内容是SystemStateData
        self.assertIn('current_state', message_data['data'])
        self.assertIn('message', message_data['data'])
        self.assertIn('progress_info', message_data['data'])

        # 验证progress_info内容
        progress_info = message_data['data']['progress_info']
        self.assertEqual(progress_info['patient_id'], "test_user_001")
        self.assertEqual(progress_info['confidence'], 1.0)
        self.assertEqual(progress_info['detection_type'], "pose_based")
        
    def test_broadcast_message_format(self):
        """测试广播消息格式"""
        test_data = {
            "patient_id": "test_user_001",
            "detection_timestamp": time.time(),
            "confidence": 1.0,
            "message": "检测到用户"
        }
        
        # 广播消息
        self.websocket_handler.broadcast_message(MessageType.USER_DETECTED, test_data)
        
        # 验证emit被调用
        self.mock_socketio.emit.assert_called_once()
        
        # 获取调用参数
        call_args = self.mock_socketio.emit.call_args
        event_name = call_args[0][0]
        message_data = call_args[0][1]
        
        # 验证事件名称
        self.assertEqual(event_name, MessageType.USER_DETECTED.value)
        
        # 验证消息格式
        self.assertIn('message_type', message_data)
        self.assertIn('session_id', message_data)
        self.assertIn('timestamp', message_data)
        self.assertIn('data', message_data)
        
        # 验证session_id正确
        self.assertEqual(message_data['session_id'], self.session_id)
        
        # 验证数据内容
        self.assertEqual(message_data['data'], test_data)
        
    def test_session_management(self):
        """测试session管理"""
        # 测试新连接的session生成
        new_client_id = "new_client_456"
        
        # 模拟连接处理
        with patch.object(self.websocket_handler, '_get_client_id', return_value=new_client_id):
            # 模拟连接事件处理逻辑
            new_session_id = str(uuid.uuid4())
            self.websocket_handler.connected_clients[new_client_id] = {
                'connect_time': time.time(),
                'last_activity': time.time(),
                'session_id': new_session_id
            }
            self.websocket_handler.client_sessions[new_client_id] = new_session_id
            
        # 验证session映射
        self.assertIn(new_client_id, self.websocket_handler.client_sessions)
        self.assertEqual(self.websocket_handler.client_sessions[new_client_id], new_session_id)
        
        # 测试断开连接的清理
        if new_client_id in self.websocket_handler.connected_clients:
            del self.websocket_handler.connected_clients[new_client_id]
        if new_client_id in self.websocket_handler.client_sessions:
            del self.websocket_handler.client_sessions[new_client_id]
            
        # 验证清理结果
        self.assertNotIn(new_client_id, self.websocket_handler.client_sessions)
        
    def test_message_type_consistency(self):
        """测试消息类型一致性"""
        # 测试所有支持的消息类型
        test_message_types = [
            MessageType.SYSTEM_STATE,
            MessageType.USER_DETECTED,
            MessageType.LOGIN_SUCCESS,
            MessageType.ACTION_READY,
            MessageType.TRAINING_INFO
        ]
        
        for message_type in test_message_types:
            with self.subTest(message_type=message_type):
                # 重置mock
                self.mock_socketio.reset_mock()
                
                # 发送消息
                test_data = {"test": f"data_for_{message_type.value}"}
                self.websocket_handler.broadcast_message(message_type, test_data)
                
                # 验证调用
                self.mock_socketio.emit.assert_called_once()
                
                # 获取调用参数
                call_args = self.mock_socketio.emit.call_args
                event_name = call_args[0][0]
                message_data = call_args[0][1]
                
                # 验证事件名称和消息类型一致
                self.assertEqual(event_name, message_type.value)
                self.assertEqual(message_data['message_type'], message_type.value)
                
                # 验证包含session_id
                self.assertIsNotNone(message_data['session_id'])
                
    def test_no_active_connections_handling(self):
        """测试无活跃连接时的处理"""
        # 清空连接
        self.websocket_handler.stats['active_connections'] = 0
        self.websocket_handler.connected_clients.clear()
        self.websocket_handler.client_sessions.clear()

        # 尝试广播消息
        self.websocket_handler.broadcast_message(MessageType.USER_DETECTED, {"test": "data"})

        # 验证没有发送消息
        self.mock_socketio.emit.assert_not_called()

    def test_handler_to_coordinator_flow(self):
        """测试处理器到协调器的数据流"""
        from models.data_models import SystemStateData
        from models.system_states import SystemState
        from services.business.system_coordinator import SystemCoordinator

        # 模拟处理器返回的结果
        handler_result = {
            "success": True,
            "trigger_event": StateTransitionEvent.USER_DETECTED,
            "next_state": SystemState.USER_LOGIN,
            "websocket_message": MessageType.USER_DETECTED,
            "state_data": SystemStateData(
                current_state=SystemState.USER_LOGIN,
                message="检测到用户",
                progress_info={
                    "patient_id": "test_user_001",
                    "detection_timestamp": time.time(),
                    "confidence": 1.0,
                    "detection_type": "pose_based"
                }
            )
        }

        # 创建系统协调器
        coordinator = SystemCoordinator()
        coordinator.websocket_handler = self.websocket_handler

        # 模拟数据处理结果发送
        coordinator._send_data_result("pose_data", handler_result)

        # 验证WebSocket消息被发送
        self.mock_socketio.emit.assert_called_once()

        # 获取调用参数
        call_args = self.mock_socketio.emit.call_args
        event_name = call_args[0][0]
        message_data = call_args[0][1]

        # 验证消息格式
        self.assertEqual(event_name, MessageType.USER_DETECTED.value)
        self.assertIn('message_type', message_data)
        self.assertIn('session_id', message_data)
        self.assertIn('timestamp', message_data)
        self.assertIn('data', message_data)

        # 验证SystemStateData内容
        data = message_data['data']
        self.assertEqual(data['current_state'], SystemState.USER_LOGIN.value)
        self.assertEqual(data['message'], "检测到用户")
        self.assertIn('progress_info', data)
        self.assertEqual(data['progress_info']['patient_id'], "test_user_001")

if __name__ == '__main__':
    unittest.main()
