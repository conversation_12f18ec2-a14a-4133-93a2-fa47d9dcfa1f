<template>
  <div class="video-stream-wrapper" :style="containerStyle">
    <!-- 加载状态 -->
    <div
      v-if="!hasVideoData"
      class="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg"
    >
      <div class="flex flex-col items-center">
        <el-icon class="animate-spin text-2xl text-blue-500 mb-2">
          <Loading />
        </el-icon>
        <span class="text-gray-600 text-sm">等待视频数据...</span>
      </div>
    </div>

    <!-- 视频流 - 直接使用data URL -->
    <img
      v-show="hasVideoData && !hasError"
      ref="videoImage"
      :src="currentFrameUrl"
      :alt="alt"
      @load="handleImageLoad"
      @error="handleImageError"
      class="w-full h-full object-cover rounded-lg camera-image"
    />

    <!-- 姿态绘制Canvas叠加层 -->
    <canvas
      v-show="hasVideoData && !hasError && showPoseOverlay"
      ref="poseCanvas"
      class="absolute inset-0 w-full h-full pointer-events-none"
      :style="{ zIndex: 10 }"
    ></canvas>

    <!-- 错误状态 -->
    <div
      v-if="hasError"
      class="absolute inset-0 flex flex-col items-center justify-center bg-red-50 rounded-lg border-2 border-red-200"
    >
      <span class="text-red-600 text-center px-4">
        视频流连接失败
        <br>
        <span class="text-sm text-red-400">系统正在自动重连，请稍候...</span>
      </span>
      <div v-if="isRetrying" class="mt-3 flex items-center text-blue-600">
        <el-icon class="animate-spin mr-2">
          <Loading />
        </el-icon>
        <span class="text-sm">重新连接中...</span>
      </div>
    </div>

    <!-- 调试信息 -->
    <div
      v-if="showDebugInfo"
      class="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded"
    >
      <div>FPS: {{ performanceStats.fps }}</div>
      <div>延迟: {{ performanceStats.latency }}ms</div>
      <div>帧数: {{ performanceStats.frameCount }}</div>
      <div>格式: {{ dataFormat }}</div>
      <div>状态: {{ currentStatus }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useMainStore } from '@/stores/main'
import { KeypointRenderer } from '@/utils/keypointRenderer'

// Props定义
const props = defineProps({
  width: {
    type: [Number, String],
    default: '100%'
  },
  height: {
    type: [Number, String],
    default: 'auto'
  },
  alt: {
    type: String,
    default: '实时视频流'
  },
  showDebugInfo: {
    type: Boolean,
    default: false
  },
  maxFPS: {
    type: Number,
    default: 30
  },
  showPoseOverlay: {
    type: Boolean,
    default: true
  },
  poseRenderOptions: {
    type: Object,
    default: () => ({
      pointRadius: 3,
      lineWidth: 2,
      confidenceThreshold: 0.3,
      showHands: true
    })
  }
})

// 使用store
const mainStore = useMainStore()
const { frameData, frameCount, poseKeypoints } = storeToRefs(mainStore)

// 响应式数据
const hasError = ref(false)
const isRetrying = ref(false)
const currentFrameUrl = ref('')
const videoImage = ref(null)
const poseCanvas = ref(null)

// 姿态渲染器
const poseRenderer = ref(null)
const isPoseRendererReady = ref(false)

// 性能统计
const performanceStats = ref({
  fps: 0,
  latency: 0,
  frameCount: 0,
  totalFrames: 0
})

// 渲染状态管理
let renderState = {
  lastRenderTime: 0,
  frameRenderCount: 0,
  fpsCalculationTime: Date.now(),
  lastFrameData: null
}

// 计算属性
const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  position: 'relative',
  minHeight: '200px'
}))

const hasVideoData = computed(() => {
  return frameData.value !== null && frameData.value.length > 0
})

const dataFormat = computed(() => {
  if (!frameData.value) return '无数据'
  return frameData.value.startsWith('data:image/') ? 'Base64' : 'Hex'
})

const currentStatus = computed(() => {
  if (!hasVideoData.value) return '等待数据'
  if (hasError.value) return '渲染错误'
  return '正常'
})

// 事件定义
const emit = defineEmits(['load', 'error'])

// 简化的渲染方法 - 直接使用data URL
const renderFrame = (frameDataValue) => {
  if (!frameDataValue) return
  
  const startTime = performance.now()
  
  try {
    // 避免重复渲染相同数据
    if (renderState.lastFrameData === frameDataValue) {
      return
    }
    
    // 检查数据格式并处理
    if (frameDataValue.startsWith('data:image/')) {
      // Base64 data URL格式 - 直接使用
      currentFrameUrl.value = frameDataValue
    } else {
      // 兼容旧的hex格式 - 转换为data URL
      const base64Data = hexToBase64(frameDataValue)
      if (base64Data) {
        currentFrameUrl.value = `data:image/jpeg;base64,${base64Data}`
      } else {
        hasError.value = true
        return
      }
    }
    
    // 更新状态
    renderState.lastFrameData = frameDataValue
    renderState.lastRenderTime = startTime
    
    // 更新性能统计
    updatePerformanceStats(startTime)
    
    console.log('视频帧渲染成功，格式:', dataFormat.value)
    
  } catch (error) {
    console.error('视频帧渲染失败:', error)
    hasError.value = true
  }
}

// 兼容性：hex转base64方法（用于向后兼容）
const hexToBase64 = (hexString) => {
  try {
    if (!hexString || hexString.length % 2 !== 0) {
      return null
    }
    
    const bytes = new Uint8Array(hexString.length / 2)
    for (let i = 0; i < bytes.length; i++) {
      bytes[i] = parseInt(hexString.substr(i * 2, 2), 16)
    }
    
    let binary = ''
    bytes.forEach(byte => binary += String.fromCharCode(byte))
    return btoa(binary)
  } catch (error) {
    console.error('hex转base64失败:', error)
    return null
  }
}

// 性能统计更新
const updatePerformanceStats = (startTime) => {
  const endTime = performance.now()
  const latency = Math.round(endTime - startTime)
  
  renderState.frameRenderCount++
  performanceStats.value.totalFrames++
  
  const now = Date.now()
  
  // 每秒更新一次FPS统计
  if (now - renderState.fpsCalculationTime >= 1000) {
    performanceStats.value.fps = renderState.frameRenderCount
    performanceStats.value.latency = latency
    performanceStats.value.frameCount = frameCount.value
    
    renderState.frameRenderCount = 0
    renderState.fpsCalculationTime = now
  }
}

// 错误处理
const handleImageLoad = () => {
  hasError.value = false
  isRetrying.value = false
  emit('load', {
    timestamp: Date.now(),
    fps: performanceStats.value.fps,
    latency: performanceStats.value.latency,
    format: dataFormat.value
  })
}

const handleImageError = () => {
  console.error('图片加载失败')
  hasError.value = true
  isRetrying.value = true
  
  // 自动重试机制
  setTimeout(() => {
    if (renderState.lastFrameData) {
      renderFrame(renderState.lastFrameData)
    }
  }, 1000)
  
  emit('error', {
    timestamp: Date.now(),
    message: '图片加载失败',
    format: dataFormat.value
  })
}

// 监听frameData变化
watch(frameData, (newFrameData) => {
  if (newFrameData && newFrameData.length > 0) {
    renderFrame(newFrameData)
  }
}, { immediate: true })

// 姿态渲染器初始化
const initializePoseRenderer = async () => {
  try {
    if (!poseCanvas.value || !props.showPoseOverlay) {
      return
    }

    console.log('🎯 初始化姿态渲染器')

    // 设置Canvas尺寸匹配视频
    const setupCanvasSize = () => {
      if (videoImage.value && poseCanvas.value) {
        const img = videoImage.value
        const canvas = poseCanvas.value

        // 获取图像的实际显示尺寸
        const rect = img.getBoundingClientRect()
        const dpr = window.devicePixelRatio || 1

        // 设置Canvas尺寸
        canvas.width = rect.width * dpr
        canvas.height = rect.height * dpr
        canvas.style.width = rect.width + 'px'
        canvas.style.height = rect.height + 'px'

        // 调整Canvas上下文的缩放
        const ctx = canvas.getContext('2d')
        ctx.scale(dpr, dpr)
      }
    }

    setupCanvasSize()

    // 创建姿态渲染器
    poseRenderer.value = new KeypointRenderer(poseCanvas.value, {
      ...props.poseRenderOptions,
      maxFPS: props.maxFPS
    })

    isPoseRendererReady.value = true

    // 如果已有姿态数据，立即渲染
    if (poseKeypoints.value && poseKeypoints.value.length > 0) {
      updatePoseRendering()
    }

    console.log('✅ 姿态渲染器初始化完成')

  } catch (error) {
    console.error('❌ 姿态渲染器初始化失败:', error)
    isPoseRendererReady.value = false
  }
}

// 更新姿态渲染
const updatePoseRendering = () => {
  if (!poseRenderer.value || !isPoseRendererReady.value || !poseKeypoints.value) {
    return
  }

  try {
    // 获取当前动作需要的关键点
    const currentAction = mainStore.currentAction
    let requiredKeypoints = []

    if (currentAction && currentAction.action_info && currentAction.action_info.required_keypoints) {
      requiredKeypoints = currentAction.action_info.required_keypoints
    } else {
      // 默认显示身体主要关键点
      requiredKeypoints = Array.from({ length: 23 }, (_, i) => i)
    }

    // 更新渲染器数据
    poseRenderer.value.updateKeypoints(poseKeypoints.value, requiredKeypoints)

    // 开始渲染循环（如果还没开始）
    if (!poseRenderer.value.isRendering) {
      poseRenderer.value.startRenderLoop()
    }

  } catch (error) {
    console.error('❌ 姿态渲染更新失败:', error)
  }
}

// 监听姿态数据变化
watch(poseKeypoints, (newKeypoints) => {
  if (newKeypoints && newKeypoints.length > 0) {
    updatePoseRendering()
  }
}, { deep: true })

// 监听视频图像加载完成，调整Canvas尺寸
watch(videoImage, () => {
  if (videoImage.value && poseCanvas.value && isPoseRendererReady.value) {
    // 延迟一帧确保图像已渲染
    setTimeout(() => {
      initializePoseRenderer()
    }, 16)
  }
})

// 生命周期
onMounted(async () => {
  console.log('📹 VideoStream组件已挂载 (Base64优化版本)')
  hasError.value = false

  if (frameData.value) {
    renderFrame(frameData.value)
  }

  // 初始化姿态渲染器
  await nextTick()
  await initializePoseRenderer()
})

onUnmounted(() => {
  console.log('📹 VideoStream组件已卸载')

  // 清理姿态渲染器
  if (poseRenderer.value) {
    poseRenderer.value.stopRenderLoop()
    poseRenderer.value = null
  }

  // 重置状态
  renderState = {
    lastRenderTime: 0,
    frameRenderCount: 0,
    fpsCalculationTime: Date.now(),
    lastFrameData: null
  }
})

// 暴露方法给父组件
defineExpose({
  getStatus: () => ({
    hasVideoData: hasVideoData.value,
    hasError: hasError.value,
    currentFPS: performanceStats.value.fps,
    renderLatency: performanceStats.value.latency,
    frameCount: performanceStats.value.frameCount,
    dataFormat: dataFormat.value
  }),
  forceRender: () => {
    if (frameData.value) {
      renderFrame(frameData.value)
    }
  }
})
</script>

<style scoped>
.video-stream-wrapper {
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 0.5rem;
  /* 硬件加速优化 */
  transform: translateZ(0);
  will-change: transform;
}

.camera-image {
  display: block;
  max-width: 100%;
  max-height: 100%;
  /* 图片渲染优化 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  /* 防止图片闪烁 */
  backface-visibility: hidden;
}

/* 加载动画优化 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .video-stream-wrapper {
    min-height: 150px;
  }
}
</style>
