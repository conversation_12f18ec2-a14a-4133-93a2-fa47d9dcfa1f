"""
智能康复系统 - 应用工厂
统一管理Flask应用和SocketIO实例的创建
"""
import logging
from flask import Flask, Response
from flask_socketio import SocketIO
from flask_cors import CORS
from typing import Dict, Any, Optional


class ApplicationFactory:
    """应用工厂 - 统一创建Flask应用和SocketIO实例"""

    @staticmethod
    def create_app(config: Optional[Dict[str, Any]] = None) -> Flask:
        """
        创建Flask应用实例
        
        Args:
            config: 应用配置字典
            
        Returns:
            Flask: 配置好的Flask应用实例
        """
        logger = logging.getLogger(__name__)
        
        try:
            # 创建Flask应用
            app = Flask(__name__)
            
            # 应用默认配置
            default_config = {
                'SECRET_KEY': 'rehabilitation_system_secret_key',
                'DEBUG': False,
                'TESTING': False
            }
            
            # 更新配置
            if config:
                default_config.update(config)
            
            app.config.update(default_config)
            
            # 启用CORS支持
            CORS(app, origins="*")
            
            logger.info("Flask应用创建完成")
            return app
            
        except Exception as e:
            logger.error(f"创建Flask应用失败: {e}")
            raise

    @staticmethod
    def create_socketio(app: Flask, config: Optional[Dict[str, Any]] = None) -> SocketIO:
        """
        创建SocketIO实例
        
        Args:
            app: Flask应用实例
            config: SocketIO配置字典
            
        Returns:
            SocketIO: 配置好的SocketIO实例
        """
        logger = logging.getLogger(__name__)
        
        try:
            # 默认SocketIO配置
            default_config = {
                'cors_allowed_origins': "*",
                'async_mode': 'eventlet',
                'ping_timeout': 60,
                'ping_interval': 25,
                'logger': True,
                'engineio_logger': True
            }
            
            # 更新配置
            if config:
                default_config.update(config)
            
            # 创建SocketIO实例
            socketio = SocketIO(app, **default_config)
            
            logger.info("SocketIO实例创建完成")
            return socketio
            
        except Exception as e:
            logger.error(f"创建SocketIO实例失败: {e}")
            raise

    @staticmethod
    def register_routes(app: Flask, components: Dict[str, Any]):
        """
        注册API路由
        
        Args:
            app: Flask应用实例
            components: 系统组件字典
        """
        logger = logging.getLogger(__name__)
        
        try:
            # 获取组件引用
            mjpeg_stream = components.get('mjpeg_stream')
            zmq_receiver = components.get('zmq_receiver')
            system_coordinator = components.get('system_coordinator')
            
            @app.route('/video_feed')
            def video_feed():
                """视频流端点 - 优化与WebSocket的兼容性"""
                try:
                    logger.info("客户端请求视频流")

                    # 检查WebSocket管理器状态，避免冲突
                    websocket_manager = components.get('websocket_manager')
                    if websocket_manager:
                        logger.debug(f"WebSocket状态: 连接={websocket_manager.is_connected}, 运行={websocket_manager.is_running}")

                    if mjpeg_stream:
                        response = mjpeg_stream.get_flask_response()
                        logger.debug("视频流响应已生成")
                        return response
                    else:
                        logger.warning("视频流服务未初始化")
                        return Response("视频流服务未初始化", status=503)

                except Exception as e:
                    logger.error(f"视频流服务错误: {e}")
                    import traceback
                    logger.error(f"错误详情: {traceback.format_exc()}")
                    return Response("视频流服务不可用", status=503)
            
            @app.route('/health')
            def health_check():
                """健康检查端点"""
                try:
                    status = {
                        'status': 'healthy',
                        'timestamp': logger.handlers[0].formatter.formatTime(
                            logging.LogRecord('', 0, '', 0, '', (), None)
                        ) if logger.handlers else None
                    }
                    
                    # 检查视频流状态
                    if mjpeg_stream:
                        status['video_stream_active'] = mjpeg_stream.is_active()
                    else:
                        status['video_stream_active'] = False
                    
                    # 检查ZMQ状态
                    if zmq_receiver:
                        status['zmq_status'] = zmq_receiver.get_connection_status()
                    else:
                        status['zmq_status'] = 'not_initialized'
                    
                    return status
                    
                except Exception as e:
                    logger.error(f"健康检查失败: {e}")
                    return {'status': 'error', 'message': str(e)}, 500

            @app.route('/api/system_status')
            def system_status():
                """系统状态查询端点"""
                try:
                    if system_coordinator:
                        return system_coordinator.get_system_status()
                    else:
                        return {'error': 'SystemCoordinator未初始化'}, 503
                except Exception as e:
                    logger.error(f"获取系统状态失败: {e}")
                    return {'error': str(e)}, 500
            logger.info("API路由注册完成")
            
        except Exception as e:
            logger.error(f"注册路由失败: {e}")
            raise

    @staticmethod
    def validate_config(config: Dict[str, Any]) -> bool:
        """
        验证配置参数
        
        Args:
            config: 配置字典
            
        Returns:
            bool: 配置是否有效
        """
        logger = logging.getLogger(__name__)
        
        try:
            # 必需的配置项
            required_keys = ['SECRET_KEY']
            
            for key in required_keys:
                if key not in config:
                    logger.error(f"缺少必需的配置项: {key}")
                    return False
            
            # 验证SocketIO配置
            if 'socketio' in config:
                socketio_config = config['socketio']
                valid_async_modes = ['eventlet', 'gevent', 'threading']
                
                if 'async_mode' in socketio_config:
                    if socketio_config['async_mode'] not in valid_async_modes:
                        logger.error(f"无效的async_mode: {socketio_config['async_mode']}")
                        return False
            
            logger.info("配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False

    @staticmethod
    def create_complete_app(config: Optional[Dict[str, Any]] = None, 
                          components: Optional[Dict[str, Any]] = None) -> tuple[Flask, SocketIO]:
        """
        创建完整的应用（Flask + SocketIO + 路由）
        
        Args:
            config: 应用配置
            components: 系统组件
            
        Returns:
            tuple: (Flask应用, SocketIO实例)
        """
        logger = logging.getLogger(__name__)
        
        try:
            # 验证配置
            if config and not ApplicationFactory.validate_config(config):
                raise ValueError("配置验证失败")
            
            # 分离Flask和SocketIO配置
            flask_config = {}
            socketio_config = {}
            
            if config:
                flask_config = {k: v for k, v in config.items() if k != 'socketio'}
                socketio_config = config.get('socketio', {})
            
            # 创建Flask应用
            app = ApplicationFactory.create_app(flask_config)
            
            # 创建SocketIO实例
            socketio = ApplicationFactory.create_socketio(app, socketio_config)
            
            # 注册路由
            if components:
                ApplicationFactory.register_routes(app, components)
            
            logger.info("完整应用创建成功")
            return app, socketio
            
        except Exception as e:
            logger.error(f"创建完整应用失败: {e}")
            raise
