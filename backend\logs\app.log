2025-07-24 16:10:25,005 - app - INFO - 日志系统初始化完成
2025-07-24 16:10:25,006 - flask_cors.core - WARNING - Unknown option passed to Flask-CORS: cors_allowed_origins
2025-07-24 16:10:25,007 - flask_cors.core - WARNING - Unknown option passed to Flask-CORS: cors_allowed_origins
2025-07-24 16:10:25,457 - app - INFO - Flask应用程序创建完成
2025-07-24 16:10:25,458 - app - INFO - API路由注册完成
2025-07-24 16:10:25,458 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:10:25,458 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:10:25,459 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:10:25,465 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:10:25,465 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:10:25,466 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:10:25,466 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:10:25,467 - app - INFO - 后台服务启动完成
2025-07-24 16:10:25,480 - werkzeug - INFO -  * Restarting with stat
2025-07-24 16:10:26,150 - app - INFO - 日志系统初始化完成
2025-07-24 16:10:26,151 - flask_cors.core - WARNING - Unknown option passed to Flask-CORS: cors_allowed_origins
2025-07-24 16:10:26,151 - flask_cors.core - WARNING - Unknown option passed to Flask-CORS: cors_allowed_origins
2025-07-24 16:10:26,504 - app - INFO - Flask应用程序创建完成
2025-07-24 16:10:26,505 - app - INFO - API路由注册完成
2025-07-24 16:10:26,505 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:10:26,505 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:10:26,505 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:10:26,523 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:10:26,523 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:10:26,523 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:10:26,523 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:10:26,524 - app - INFO - 后台服务启动完成
2025-07-24 16:10:26,527 - werkzeug - WARNING -  * Debugger is active!
2025-07-24 16:10:26,545 - werkzeug - INFO -  * Debugger PIN: 358-768-143
2025-07-24 16:12:39,322 - app - INFO - 日志系统初始化完成
2025-07-24 16:12:39,707 - app - INFO - Flask应用程序创建完成
2025-07-24 16:12:39,708 - app - INFO - API路由注册完成
2025-07-24 16:12:39,708 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:12:39,708 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:12:39,709 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:12:39,732 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:12:39,732 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:12:39,733 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:12:39,733 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:12:39,733 - app - INFO - 后台服务启动完成
2025-07-24 16:12:39,738 - werkzeug - INFO -  * Restarting with stat
2025-07-24 16:12:40,378 - app - INFO - 日志系统初始化完成
2025-07-24 16:12:40,699 - app - INFO - Flask应用程序创建完成
2025-07-24 16:12:40,700 - app - INFO - API路由注册完成
2025-07-24 16:12:40,700 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:12:40,700 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:12:40,700 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:12:40,716 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:12:40,716 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:12:40,717 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:12:40,717 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:12:40,717 - app - INFO - 后台服务启动完成
2025-07-24 16:12:40,721 - werkzeug - WARNING -  * Debugger is active!
2025-07-24 16:12:40,738 - werkzeug - INFO -  * Debugger PIN: 358-768-143
2025-07-24 16:13:21,682 - app - INFO - 日志系统初始化完成
2025-07-24 16:13:22,010 - app - INFO - Flask应用程序创建完成
2025-07-24 16:13:22,010 - app - INFO - API路由注册完成
2025-07-24 16:13:22,011 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:13:22,011 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:13:22,011 - root - ERROR - 系统启动异常: 'RehabilitationApp' object has no attribute 'run'
2025-07-24 16:13:33,328 - app - INFO - 日志系统初始化完成
2025-07-24 16:13:33,648 - app - INFO - Flask应用程序创建完成
2025-07-24 16:13:33,649 - app - INFO - API路由注册完成
2025-07-24 16:13:33,649 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:13:33,649 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:13:33,650 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:13:33,654 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:13:33,656 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:13:33,657 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:13:33,657 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:13:33,657 - app - INFO - 后台服务启动完成
2025-07-24 16:13:33,661 - werkzeug - INFO -  * Restarting with stat
2025-07-24 16:13:34,284 - app - INFO - 日志系统初始化完成
2025-07-24 16:13:34,606 - app - INFO - Flask应用程序创建完成
2025-07-24 16:13:34,607 - app - INFO - API路由注册完成
2025-07-24 16:13:34,607 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:13:34,607 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:13:34,607 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:13:34,625 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:13:34,625 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:13:34,626 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:13:34,626 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:13:34,626 - app - INFO - 后台服务启动完成
2025-07-24 16:13:34,630 - werkzeug - WARNING -  * Debugger is active!
2025-07-24 16:13:34,646 - werkzeug - INFO -  * Debugger PIN: 358-768-143
2025-07-24 16:13:48,365 - app - INFO - 日志系统初始化完成
2025-07-24 16:13:48,690 - app - INFO - Flask应用程序创建完成
2025-07-24 16:13:48,691 - app - INFO - API路由注册完成
2025-07-24 16:13:48,691 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:13:48,691 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:13:48,691 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:13:48,696 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:13:48,696 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:13:48,697 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:13:48,697 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:13:48,697 - app - INFO - 后台服务启动完成
2025-07-24 16:13:48,701 - werkzeug - INFO -  * Restarting with stat
2025-07-24 16:13:49,335 - app - INFO - 日志系统初始化完成
2025-07-24 16:13:49,657 - app - INFO - Flask应用程序创建完成
2025-07-24 16:13:49,658 - app - INFO - API路由注册完成
2025-07-24 16:13:49,658 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:13:49,658 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:13:49,658 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:13:49,676 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:13:49,676 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:13:49,677 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:13:49,678 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:13:49,678 - app - INFO - 后台服务启动完成
2025-07-24 16:13:49,682 - werkzeug - WARNING -  * Debugger is active!
2025-07-24 16:13:49,708 - werkzeug - INFO -  * Debugger PIN: 358-768-143
2025-07-24 16:13:54,095 - werkzeug - INFO -  * Detected change in 'D:\\project\\digital-screen\\kf-v3\\backend\\services\\communication\\__init__.py', reloading
2025-07-24 16:13:54,153 - werkzeug - INFO -  * Restarting with stat
2025-07-24 16:13:54,730 - app - INFO - 日志系统初始化完成
2025-07-24 16:13:55,045 - app - INFO - Flask应用程序创建完成
2025-07-24 16:13:55,046 - app - INFO - API路由注册完成
2025-07-24 16:13:55,046 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:13:55,046 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:13:55,047 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:13:55,058 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:13:55,058 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:13:55,059 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:13:55,059 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:13:55,059 - app - INFO - 后台服务启动完成
2025-07-24 16:13:55,062 - werkzeug - WARNING -  * Debugger is active!
2025-07-24 16:13:55,078 - werkzeug - INFO -  * Debugger PIN: 358-768-143
2025-07-24 16:15:42,474 - app - INFO - 日志系统初始化完成
2025-07-24 16:15:42,803 - app - INFO - Flask应用程序创建完成
2025-07-24 16:15:42,804 - app - INFO - API路由注册完成
2025-07-24 16:15:42,804 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:15:42,804 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:15:42,804 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:15:42,809 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:15:42,809 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:15:42,809 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:15:42,810 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:15:42,810 - app - INFO - 后台服务启动完成
2025-07-24 16:15:42,813 - werkzeug - INFO -  * Restarting with stat
2025-07-24 16:15:43,433 - app - INFO - 日志系统初始化完成
2025-07-24 16:15:43,753 - app - INFO - Flask应用程序创建完成
2025-07-24 16:15:43,754 - app - INFO - API路由注册完成
2025-07-24 16:15:43,754 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:15:43,754 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:15:43,754 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:15:43,768 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:15:43,768 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:15:43,768 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:15:43,768 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:15:43,769 - app - INFO - 后台服务启动完成
2025-07-24 16:15:43,773 - werkzeug - WARNING -  * Debugger is active!
2025-07-24 16:15:43,789 - werkzeug - INFO -  * Debugger PIN: 358-768-143
2025-07-24 16:17:25,492 - app - INFO - 日志系统初始化完成
2025-07-24 16:17:25,798 - app - INFO - Flask应用程序创建完成
2025-07-24 16:17:25,799 - app - INFO - API路由注册完成
2025-07-24 16:17:25,799 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:17:25,799 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:17:25,799 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:17:25,804 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:17:25,804 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:17:25,805 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:17:25,805 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:17:25,805 - app - INFO - 后台服务启动完成
2025-07-24 16:17:25,809 - werkzeug - INFO -  * Restarting with stat
2025-07-24 16:17:26,391 - app - INFO - 日志系统初始化完成
2025-07-24 16:17:26,710 - app - INFO - Flask应用程序创建完成
2025-07-24 16:17:26,711 - app - INFO - API路由注册完成
2025-07-24 16:17:26,711 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:17:26,712 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:17:26,712 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:17:26,715 - werkzeug - WARNING -  * Debugger is active!
2025-07-24 16:17:26,733 - werkzeug - INFO -  * Debugger PIN: 358-768-143
2025-07-24 16:18:53,561 - app - INFO - 日志系统初始化完成
2025-07-24 16:18:53,869 - app - INFO - Flask应用程序创建完成
2025-07-24 16:18:53,869 - app - INFO - API路由注册完成
2025-07-24 16:18:53,870 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:18:53,870 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:18:53,870 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:18:53,874 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:18:53,875 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:18:53,875 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:18:53,875 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:18:53,875 - app - INFO - 后台服务启动完成
2025-07-24 16:19:22,348 - app - INFO - 日志系统初始化完成
2025-07-24 16:19:23,597 - app - INFO - Flask应用程序创建完成
2025-07-24 16:19:23,598 - app - INFO - API路由注册完成
2025-07-24 16:19:23,598 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:19:23,598 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:19:23,598 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:19:23,624 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:19:23,624 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:19:23,625 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:19:23,625 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:19:23,626 - app - INFO - 后台服务启动完成
2025-07-24 16:19:23,634 - werkzeug - INFO -  * Restarting with stat
2025-07-24 16:19:24,384 - app - INFO - 日志系统初始化完成
2025-07-24 16:19:24,894 - app - INFO - Flask应用程序创建完成
2025-07-24 16:19:24,895 - app - INFO - API路由注册完成
2025-07-24 16:19:24,895 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 16:19:24,895 - app - INFO - WebSocket处理器初始化完成
2025-07-24 16:19:24,896 - app - INFO - 启动康复系统服务器 - 0.0.0.0:5000
2025-07-24 16:19:24,903 - services.communication.zmq.zmq_receiver - INFO - ZMQ接收器初始化成功 - 检测端口: 6070, 摄像头端口: 6080
2025-07-24 16:19:24,904 - services.communication.zmq.zmq_receiver - INFO - 开始ZMQ数据接收...
2025-07-24 16:19:24,905 - services.communication.zmq.zmq_receiver - INFO - 🎬 摄像头帧接收循环已启动
2025-07-24 16:19:24,905 - app - INFO - ZMQ数据接收器启动成功
2025-07-24 16:19:24,905 - app - INFO - 后台服务启动完成
2025-07-24 16:19:24,910 - werkzeug - WARNING -  * Debugger is active!
2025-07-24 16:19:24,935 - werkzeug - INFO -  * Debugger PIN: 358-768-143
2025-07-24 17:43:14,870 - app - INFO - 日志系统初始化完成
2025-07-24 17:43:15,394 - app - INFO - Flask应用程序创建完成
2025-07-24 17:43:15,395 - app - INFO - API路由注册完成
2025-07-24 17:43:15,395 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 17:43:15,395 - services.business.system_coordinator - INFO - WebSocket处理器已连接到系统协调器
2025-07-24 17:43:15,395 - app - INFO - WebSocket处理器初始化完成
2025-07-24 17:43:15,396 - app - INFO - 日志系统初始化完成
2025-07-24 17:43:15,396 - app - INFO - Flask应用程序创建完成
2025-07-24 17:43:15,397 - app - INFO - API路由注册完成
2025-07-24 17:43:15,397 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 17:43:15,397 - services.business.system_coordinator - INFO - WebSocket处理器已连接到系统协调器
2025-07-24 17:43:15,397 - app - INFO - WebSocket处理器初始化完成
2025-07-24 17:46:36,018 - app - INFO - 日志系统初始化完成
2025-07-24 17:46:36,399 - app - INFO - Flask应用程序创建完成
2025-07-24 17:46:36,400 - app - INFO - API路由注册完成
2025-07-24 17:46:36,401 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 17:46:36,401 - services.business.system_coordinator - INFO - WebSocket处理器已连接到系统协调器
2025-07-24 17:46:36,401 - app - INFO - WebSocket处理器初始化完成
2025-07-24 17:46:36,402 - app - INFO - 日志系统初始化完成
2025-07-24 17:46:36,402 - app - INFO - Flask应用程序创建完成
2025-07-24 17:46:36,403 - app - INFO - API路由注册完成
2025-07-24 17:46:36,403 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 17:46:36,403 - services.business.system_coordinator - INFO - WebSocket处理器已连接到系统协调器
2025-07-24 17:46:36,403 - app - INFO - WebSocket处理器初始化完成
2025-07-24 17:50:20,576 - app - INFO - 日志系统初始化完成
2025-07-24 17:50:20,948 - app - INFO - Flask应用程序创建完成
2025-07-24 17:50:20,949 - app - INFO - API路由注册完成
2025-07-24 17:50:20,950 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 17:50:20,950 - services.business.system_coordinator - INFO - WebSocket处理器已连接到系统协调器
2025-07-24 17:50:20,950 - app - INFO - WebSocket处理器初始化完成
2025-07-24 17:50:20,950 - app - INFO - 日志系统初始化完成
2025-07-24 17:50:20,951 - app - INFO - Flask应用程序创建完成
2025-07-24 17:50:20,952 - app - INFO - API路由注册完成
2025-07-24 17:50:20,952 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 17:50:20,952 - services.business.system_coordinator - INFO - WebSocket处理器已连接到系统协调器
2025-07-24 17:50:20,953 - app - INFO - WebSocket处理器初始化完成
2025-07-24 18:10:15,440 - app - INFO - 日志系统初始化完成
2025-07-24 18:10:15,760 - app - INFO - Flask应用程序创建完成
2025-07-24 18:10:15,761 - app - INFO - API路由注册完成
2025-07-24 18:10:15,761 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 18:10:15,761 - services.business.system_coordinator - INFO - WebSocket处理器已连接到系统协调器
2025-07-24 18:10:15,761 - app - INFO - WebSocket处理器初始化完成
2025-07-24 18:10:55,385 - app - INFO - 日志系统初始化完成
2025-07-24 18:10:55,698 - app - INFO - Flask应用程序创建完成
2025-07-24 18:10:55,700 - app - INFO - API路由注册完成
2025-07-24 18:10:55,700 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 18:10:55,700 - services.business.system_coordinator - INFO - WebSocket处理器已连接到系统协调器
2025-07-24 18:10:55,700 - app - INFO - WebSocket处理器初始化完成
2025-07-24 18:10:55,713 - werkzeug - INFO -  * Restarting with stat
2025-07-24 18:10:56,346 - app - INFO - 日志系统初始化完成
2025-07-24 18:10:56,661 - app - INFO - Flask应用程序创建完成
2025-07-24 18:10:56,662 - app - INFO - API路由注册完成
2025-07-24 18:10:56,662 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 18:10:56,662 - services.business.system_coordinator - INFO - WebSocket处理器已连接到系统协调器
2025-07-24 18:10:56,662 - app - INFO - WebSocket处理器初始化完成
2025-07-24 18:10:56,666 - werkzeug - WARNING -  * Debugger is active!
2025-07-24 18:10:56,685 - werkzeug - INFO -  * Debugger PIN: 358-768-143
2025-07-24 23:12:02,484 - __main__ - INFO - 日志系统初始化完成
2025-07-24 23:12:02,950 - __main__ - INFO - Flask应用程序创建完成
2025-07-24 23:12:02,952 - __main__ - INFO - API路由注册完成
2025-07-24 23:12:02,952 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 23:12:02,952 - services.business.system_coordinator - INFO - WebSocket处理器已连接到系统协调器
2025-07-24 23:12:02,952 - __main__ - INFO - WebSocket处理器初始化完成
2025-07-24 23:12:47,009 - app - INFO - 日志系统初始化完成
2025-07-24 23:12:47,365 - app - INFO - Flask应用程序创建完成
2025-07-24 23:12:47,366 - app - INFO - API路由注册完成
2025-07-24 23:12:47,366 - services.communication.websocket.websocket_handler - INFO - SocketIO事件注册完成
2025-07-24 23:12:47,366 - services.business.system_coordinator - INFO - WebSocket处理器已连接到系统协调器
2025-07-24 23:12:47,367 - app - INFO - WebSocket处理器初始化完成
2025-07-27 21:51:49,957 - __main__ - INFO - 日志系统初始化完成
2025-07-27 21:51:49,957 - __main__ - INFO - ==================================================
2025-07-27 21:51:49,957 - __main__ - INFO - 智能康复系统 - 简化版启动
2025-07-27 21:51:49,957 - __main__ - INFO - ==================================================
2025-07-27 21:51:49,992 - __main__ - INFO - Flask + SocketIO 初始化完成
2025-07-27 21:51:49,992 - __main__ - INFO - 路由注册完成
2025-07-27 21:51:49,993 - __main__ - INFO - WebSocket处理器设置完成
2025-07-27 21:51:50,036 - simple_zmq - INFO - ZMQ回调函数已设置
2025-07-27 21:51:50,044 - simple_zmq - INFO - ZMQ接收器启动成功 - 检测端口:6070, 摄像头端口:6080
2025-07-27 21:51:50,044 - __main__ - INFO - 简化ZMQ接收器启动成功
2025-07-27 21:51:50,044 - __main__ - INFO - 启动Web服务器 - 0.0.0.0:5000
2025-07-27 21:51:50,085 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,088 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-27 21:51:50,088 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-27 21:51:50,123 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,156 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,160 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,199 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,242 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,274 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,279 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,317 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,356 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,388 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,395 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,433 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,471 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,505 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,510 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,548 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,585 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,621 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,624 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,661 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,700 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,735 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,740 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,777 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,815 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,849 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,854 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,891 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,928 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,961 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:50,966 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:51,004 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:51,042 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:51,075 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:51,080 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:51,107 - __main__ - INFO - 接收到信号 2，开始关闭...
2025-07-27 21:51:51,107 - __main__ - INFO - 开始关闭应用...
2025-07-27 21:51:51,110 - simple_zmq - INFO - ZMQ接收器已停止
2025-07-27 21:51:51,110 - __main__ - INFO - 简化ZMQ接收器已停止
2025-07-27 21:51:51,110 - __main__ - INFO - 应用已关闭
2025-07-27 21:51:56,994 - simple_app - INFO - 日志系统初始化完成
2025-07-27 21:51:56,994 - simple_app - INFO - ==================================================
2025-07-27 21:51:56,994 - simple_app - INFO - 智能康复系统 - 简化版启动
2025-07-27 21:51:56,994 - simple_app - INFO - ==================================================
2025-07-27 21:51:57,021 - simple_app - INFO - Flask + SocketIO 初始化完成
2025-07-27 21:51:57,021 - simple_app - INFO - 路由注册完成
2025-07-27 21:51:57,021 - simple_app - INFO - WebSocket处理器设置完成
2025-07-27 21:51:57,048 - simple_zmq - INFO - ZMQ回调函数已设置
2025-07-27 21:51:57,066 - simple_zmq - INFO - ZMQ接收器启动成功 - 检测端口:6070, 摄像头端口:6080
2025-07-27 21:51:57,066 - simple_app - INFO - 简化ZMQ接收器启动成功
2025-07-27 21:51:57,066 - simple_app - INFO - 启动Web服务器 - 0.0.0.0:5000
2025-07-27 21:51:57,107 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-07-27 21:51:57,107 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-27 21:51:57,125 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,157 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,164 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,201 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,240 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,273 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,277 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,316 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,354 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,388 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,391 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,429 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,468 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,502 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,507 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,545 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,583 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,616 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,621 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,660 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,697 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,730 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,734 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,773 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,811 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,844 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,850 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,888 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,926 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,958 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:57,964 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,001 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,039 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,072 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,078 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,117 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,155 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,193 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,198 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,232 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,270 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,302 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,309 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,347 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,386 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,424 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,424 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,463 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,500 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,533 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,538 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,577 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,615 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,648 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,653 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,690 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,729 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,760 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,767 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,805 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,844 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,876 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,883 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,921 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,959 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,993 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:58,998 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,050 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,082 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,088 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,127 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,166 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,200 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,204 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,243 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,280 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,314 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,319 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,358 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,396 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,430 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,434 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,472 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,510 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,544 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,548 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,586 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,624 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,657 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,663 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,700 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,738 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,770 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,779 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,817 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,856 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,889 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,894 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,933 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:51:59,971 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:52:00,004 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:52:00,008 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:52:00,046 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:52:00,085 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:52:00,118 - simple_zmq - ERROR - 处理检测数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:52:00,124 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:52:00,160 - simple_zmq - ERROR - 处理摄像头数据失败: Expecting value: line 1 column 1 (char 0)
2025-07-27 21:52:00,183 - simple_app - INFO - 接收到信号 2，开始关闭...
2025-07-27 21:52:00,183 - simple_app - INFO - 开始关闭应用...
2025-07-27 21:52:00,185 - simple_zmq - INFO - ZMQ接收器已停止
2025-07-27 21:52:00,185 - simple_app - INFO - 简化ZMQ接收器已停止
2025-07-27 21:52:00,185 - simple_app - INFO - 应用已关闭
