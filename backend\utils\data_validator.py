"""
智能康复系统 - 数据验证工具
提供数据验证和转换功能
"""
import logging
from typing import Dict, Any, Optional
from models.data_models import DataValidator as ModelDataValidator

class SchemaValidator:
    """数据模式验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.logger = logging.getLogger(__name__)
    
    def validate_zmq_detect_data(self, data: Dict[str, Any]) -> bool:
        """
        验证ZMQ检测数据格式
        
        Args:
            data: 待验证的数据字典
            
        Returns:
            bool: 验证是否通过
        """
        try:
            return ModelDataValidator.validate_zmq_detect_data(data)
        except Exception as e:
            self.logger.error(f"ZMQ检测数据验证失败: {e}")
            return False
    
    def validate_zmq_camera_frame(self, data: Dict[str, Any]) -> bool:
        """
        验证ZMQ摄像头帧数据格式
        
        Args:
            data: 待验证的数据字典
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 检查必需字段
            required_fields = ['timestamp', 'frame_data', 'frame_shape']
            if not all(field in data for field in required_fields):
                return False
            
            # 验证数据类型
            if not isinstance(data['timestamp'], (int, float)):
                return False
            
            if not isinstance(data['frame_data'], str):
                return False
            
            if not isinstance(data['frame_shape'], list) or len(data['frame_shape']) != 3:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"ZMQ摄像头帧数据验证失败: {e}")
            return False

class DataConverter:
    """数据转换器"""
    
    def __init__(self):
        """初始化转换器"""
        self.logger = logging.getLogger(__name__)
    
    def convert_to_standard_format(self, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        转换数据到标准格式
        
        Args:
            data: 原始数据
            
        Returns:
            Dict: 标准格式数据，失败返回None
        """
        try:
            # 这里可以添加数据格式转换逻辑
            # 目前直接返回原数据
            return data
            
        except Exception as e:
            self.logger.error(f"数据转换失败: {e}")
            return None

# 全局实例
schema_validator = SchemaValidator()
data_converter = DataConverter()
