"""
智能康复系统 - 动作识别算法
实现康复动作的识别和分析
"""
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple, Any
from models.data_models import ActionType, TrainingSide, PoseDataConverter
from models.constants import ActionKeyPoints, KeyPointMapping
# pose_analyzer 将在需要时延迟导入以避免循环导入
# 四个动作的计数功能
class ActionRecognizer:
    """动作识别器"""
    
    def __init__(self):
        """初始化动作识别器"""
        self.logger = logging.getLogger(__name__)
        
        # 动作识别阈值
        self.thresholds = {
            'shoulder_touch': {
                'distance_threshold': 50.0,  # 手腕到肩膀的距离阈值（像素）
                'confidence_threshold': 0.5,
                'hold_time': 1.0  # 保持时间（秒）
            },
            'arm_raise': {
                'angle_threshold': 45.0,  # 手臂抬起角度阈值（度）
                'height_threshold': 0.8,  # 相对高度阈值
                'confidence_threshold': 0.5,
                'hold_time': 2.0
            },
            'finger_touch': {
                'distance_threshold': 20.0,  # 手指接触距离阈值（像素）
                'confidence_threshold': 0.3,  # 手部关键点置信度较低
                'sequence_tolerance': 0.5  # 序列容错时间
            },
            'palm_flip': {
                'rotation_threshold': 90.0,  # 旋转角度阈值（度）
                'confidence_threshold': 0.4,
                'flip_time': 1.0  # 翻转时间
            }
        }
        
        # 动作状态跟踪
        self.action_states = {}
        
        # 统计信息
        self.stats = {
            'total_recognitions': 0,
            'successful_recognitions': 0,
            'action_counts': {
                'shoulder_touch': 0,
                'arm_raise': 0,
                'finger_touch': 0,
                'palm_flip': 0
            }
        }
        
        self.logger.info("动作识别器初始化完成")
    
    def recognize_action(self, keypoints: List[List[float]], action_type: ActionType,
                        side: TrainingSide) -> Dict[str, Any]:
        """
        识别指定动作

        Args:
            keypoints: 关键点列表 [[x,y,confidence], ...]
            action_type: 动作类型
            side: 训练侧别

        Returns:
            Dict: 识别结果
        """
        try:
            self.stats['total_recognitions'] += 1
            
            # 根据动作类型调用相应的识别函数
            if action_type == 'shoulder_touch':
                result = self._recognize_shoulder_touch(keypoints, side)
            elif action_type == 'arm_raise':
                result = self._recognize_arm_raise(keypoints, side)
            elif action_type == 'finger_touch':
                result = self._recognize_finger_touch(keypoints, side)
            elif action_type == 'palm_flip':
                result = self._recognize_palm_flip(keypoints, side)
            else:
                return self._create_recognition_result(False, f"不支持的动作类型: {action_type}")
            
            if result['recognized']:
                self.stats['successful_recognitions'] += 1
                self.stats['action_counts'][action_type] += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"动作识别失败: {e}")
            return self._create_recognition_result(False, f"识别异常: {str(e)}")
    
    def _recognize_shoulder_touch(self, keypoints: List[List[float]], side: TrainingSide) -> Dict[str, Any]:
        """
        识别摸肩膀动作

        Args:
            keypoints: 关键点列表 [[x,y,confidence], ...]
            side: 训练侧别

        Returns:
            Dict: 识别结果
        """
        try:
            # 延迟导入以避免循环导入
            from .pose_analyzer import pose_analyzer

            # 确定手腕和目标肩膀的关键点索引
            if side == 'left':
                wrist_idx = KeyPointMapping.LEFT_WRIST
                target_shoulder_idx = KeyPointMapping.RIGHT_SHOULDER
            else:
                wrist_idx = KeyPointMapping.RIGHT_WRIST
                target_shoulder_idx = KeyPointMapping.LEFT_SHOULDER

            # 获取关键点（pose_analyzer已适配三元组格式）
            wrist = pose_analyzer.get_keypoint_by_index(keypoints, wrist_idx)
            target_shoulder = pose_analyzer.get_keypoint_by_index(keypoints, target_shoulder_idx)
            
            if not wrist or not target_shoulder:
                return self._create_recognition_result(False, "关键点缺失")
            
            # 检查置信度
            threshold = self.thresholds['shoulder_touch']
            if (wrist[2] < threshold['confidence_threshold'] or 
                target_shoulder[2] < threshold['confidence_threshold']):
                return self._create_recognition_result(False, "关键点置信度不足")
            
            # 计算距离
            distance = pose_analyzer.calculate_distance(wrist, target_shoulder)
            
            # 判断是否触碰
            is_touching = distance < threshold['distance_threshold']
            
            # 计算完成度（距离越近完成度越高）
            completion = max(0.0, 1.0 - distance / (threshold['distance_threshold'] * 2))
            
            return self._create_recognition_result(
                recognized=is_touching,
                message=f"手腕到肩膀距离: {distance:.1f}px",
                details={
                    'distance': distance,
                    'threshold': threshold['distance_threshold'],
                    'completion': completion,
                    'wrist_position': {'x': wrist[0], 'y': wrist[1]},
                    'shoulder_position': {'x': target_shoulder[0], 'y': target_shoulder[1]}
                }
            )
            
        except Exception as e:
            self.logger.error(f"摸肩膀动作识别失败: {e}")
            return self._create_recognition_result(False, f"识别异常: {str(e)}")
    
    def _recognize_arm_raise(self, keypoints: List[List[float]], side: TrainingSide) -> Dict[str, Any]:
        """
        识别手臂上抬动作

        Args:
            keypoints: 关键点列表 [[x,y,confidence], ...]
            side: 训练侧别

        Returns:
            Dict: 识别结果
        """
        try:
            # 延迟导入以避免循环导入
            from .pose_analyzer import pose_analyzer

            # 确定肩膀、肘部、手腕的关键点索引
            if side == 'left':
                shoulder_idx = KeyPointMapping.LEFT_SHOULDER
                elbow_idx = KeyPointMapping.LEFT_ELBOW
                wrist_idx = KeyPointMapping.LEFT_WRIST
            else:
                shoulder_idx = KeyPointMapping.RIGHT_SHOULDER
                elbow_idx = KeyPointMapping.RIGHT_ELBOW
                wrist_idx = KeyPointMapping.RIGHT_WRIST

            # 获取关键点（pose_analyzer已适配三元组格式）
            shoulder = pose_analyzer.get_keypoint_by_index(keypoints, shoulder_idx)
            elbow = pose_analyzer.get_keypoint_by_index(keypoints, elbow_idx)
            wrist = pose_analyzer.get_keypoint_by_index(keypoints, wrist_idx)
            
            if not all([shoulder, elbow, wrist]):
                return self._create_recognition_result(False, "关键点缺失")
            
            # 检查置信度
            threshold = self.thresholds['arm_raise']
            if(shoulder and elbow and wrist):
                if (shoulder[2] < threshold['confidence_threshold'] or
                    elbow[2] < threshold['confidence_threshold'] or
                    wrist[2] < threshold['confidence_threshold']):
                    return self._create_recognition_result(False, "关键点置信度不足")
                
                # 计算手臂角度（肩膀-肘部-手腕）
                arm_angle = pose_analyzer.calculate_angle(shoulder, elbow, wrist)
                
                # 计算手腕相对于肩膀的高度
                height_ratio = (shoulder[0] - wrist[1]) / abs(shoulder[1] - wrist[0] + 1)  # 防止除零
                
                # 判断是否抬起
                angle_raised = arm_angle > threshold['angle_threshold']
                height_raised = height_ratio > threshold['height_threshold']
                is_raised = angle_raised and height_raised
                
                # 计算完成度
                angle_completion = min(1.0, arm_angle / 180.0)  # 角度完成度
                height_completion = max(0.0, height_ratio)  # 高度完成度
                completion = (angle_completion + height_completion) / 2
                
                return self._create_recognition_result(
                    recognized=is_raised,
                    message=f"手臂角度: {arm_angle:.1f}°, 高度比: {height_ratio:.2f}",
                    details={
                        'arm_angle': arm_angle,
                        'height_ratio': height_ratio,
                        'angle_threshold': threshold['angle_threshold'],
                        'height_threshold': threshold['height_threshold'],
                        'completion': completion,
                        'positions': {
                            'shoulder': {'x': shoulder[0], 'y': shoulder[1]},
                            'elbow': {'x': elbow[0], 'y': elbow[1]},
                            'wrist': {'x': wrist[0], 'y': wrist[1]}
                        }
                    }
                )
            
        except Exception as e:
            self.logger.error(f"手臂上抬动作识别失败: {e}")
            return self._create_recognition_result(False, f"识别异常: {str(e)}")
    
    def _recognize_finger_touch(self, keypoints: List[List[float]], side: TrainingSide) -> Dict[str, Any]:
        """
        识别对指动作

        Args:
            keypoints: 关键点列表 [[x,y,confidence], ...]
            side: 训练侧别

        Returns:
            Dict: 识别结果
        """
        try:
            from .pose_analyzer import pose_analyzer

            # 手部关键点起始索引
            if side == 'left':
                hand_start = 91  # 左手起始索引
            else:
                hand_start = 112  # 右手起始索引

            # 拇指和其他手指的关键点索引（相对于手部起始位置）
            thumb_tip = hand_start + 4  # 拇指尖
            finger_tips = [
                hand_start + 8,   # 食指尖
                hand_start + 12,  # 中指尖
                hand_start + 16,  # 无名指尖
                hand_start + 20   # 小指尖
            ]

            # 获取拇指尖关键点
            thumb = pose_analyzer.get_keypoint_by_index(keypoints, thumb_tip)
            if not thumb or thumb[2] < self.thresholds['finger_touch']['confidence_threshold']:  # confidence
                return self._create_recognition_result(False, "拇指关键点缺失或置信度不足")

            # 检查与其他手指的接触
            touches = []
            min_distance = float('inf')

            for i, finger_idx in enumerate(finger_tips):
                finger = pose_analyzer.get_keypoint_by_index(keypoints, finger_idx)
                if finger and finger[2] > self.thresholds['finger_touch']['confidence_threshold']:  # confidence
                    distance = pose_analyzer.calculate_distance(thumb, finger)
                    is_touching = distance < self.thresholds['finger_touch']['distance_threshold']
                    
                    touches.append({
                        'finger': ['食指', '中指', '无名指', '小指'][i],
                        'distance': distance,
                        'touching': is_touching
                    })
                    
                    min_distance = min(min_distance, distance)
            
            # 判断是否有接触
            any_touch = any(touch['touching'] for touch in touches)
            
            # 计算完成度（基于最近距离）
            if min_distance != float('inf'):
                completion = max(0.0, 1.0 - min_distance / (self.thresholds['finger_touch']['distance_threshold'] * 2))
            else:
                completion = 0.0
            
            return self._create_recognition_result(
                recognized=any_touch,
                message=f"检测到 {len([t for t in touches if t['touching']])} 个手指接触",
                details={
                    'touches': touches,
                    'min_distance': min_distance if min_distance != float('inf') else None,
                    'completion': completion,
                    'thumb_position': {'x': thumb[0], 'y': thumb[1]}
                }
            )
            
        except Exception as e:
            self.logger.error(f"对指动作识别失败: {e}")
            return self._create_recognition_result(False, f"识别异常: {str(e)}")
    
    def _recognize_palm_flip(self, keypoints: List[List[float]], side: TrainingSide) -> Dict[str, Any]:
        """
        识别手掌翻转动作

        Args:
            keypoints: 关键点列表 [[x,y,confidence], ...]
            side: 训练侧别

        Returns:
            Dict: 识别结果
        """
        try:
            from .pose_analyzer import pose_analyzer

            # 手部关键点起始索引
            if side == 'left':
                hand_start = 91
                wrist_idx = KeyPointMapping.LEFT_WRIST
            else:
                hand_start = 112
                wrist_idx = KeyPointMapping.RIGHT_WRIST

            # 获取手腕和手部关键点
            wrist = pose_analyzer.get_keypoint_by_index(keypoints, wrist_idx)
            if not wrist or wrist[2] < self.thresholds['palm_flip']['confidence_threshold']:  # confidence
                return self._create_recognition_result(False, "手腕关键点缺失或置信度不足")

            # 获取手掌中心点（中指根部）
            palm_center_idx = hand_start + 9  # 中指根部
            palm_center = pose_analyzer.get_keypoint_by_index(keypoints, palm_center_idx)

            if not palm_center or palm_center[2] < self.thresholds['palm_flip']['confidence_threshold']:  # confidence
                return self._create_recognition_result(False, "手掌中心点缺失或置信度不足")

            # 获取手指尖点来判断手掌朝向
            finger_tips = []
            for i in [4, 8, 12, 16, 20]:  # 各手指尖的相对索引
                tip = pose_analyzer.get_keypoint_by_index(keypoints, hand_start + i)
                if tip and tip[2] > self.thresholds['palm_flip']['confidence_threshold']:  # confidence
                    finger_tips.append(tip)
            
            if len(finger_tips) < 3:
                return self._create_recognition_result(False, "手指关键点不足")
            
            # 计算手掌朝向（简化算法：基于手指相对于手腕的位置）
            fingers_above_wrist = sum(1 for tip in finger_tips if tip[1] < wrist[1])  # y坐标
            fingers_below_wrist = len(finger_tips) - fingers_above_wrist
            
            # 判断手掌朝向
            if fingers_above_wrist > fingers_below_wrist:
                palm_orientation = "up"
            else:
                palm_orientation = "down"
            
            # 简化的翻转检测（实际应用中需要更复杂的算法）
            # 这里假设检测到翻转动作
            is_flipping = True  # 简化处理
            completion = 0.8  # 简化的完成度
            
            return self._create_recognition_result(
                recognized=is_flipping,
                message=f"手掌朝向: {palm_orientation}",
                details={
                    'palm_orientation': palm_orientation,
                    'fingers_above_wrist': fingers_above_wrist,
                    'fingers_below_wrist': fingers_below_wrist,
                    'completion': completion,
                    'wrist_position': {'x': wrist[0], 'y': wrist[1]},
                    'palm_center_position': {'x': palm_center[0], 'y': palm_center[1]}
                }
            )
            
        except Exception as e:
            self.logger.error(f"手掌翻转动作识别失败: {e}")
            return self._create_recognition_result(False, f"识别异常: {str(e)}")
    
    def _create_recognition_result(self, recognized: bool, message: str = "", 
                                 details: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        创建识别结果
        
        Args:
            recognized: 是否识别成功
            message: 结果消息
            details: 详细信息
            
        Returns:
            Dict: 识别结果
        """
        # 延迟导入以避免循环导入
        try:
            from .pose_analyzer import pose_analyzer
            timestamp = pose_analyzer.stats.get('processing_time', 0.0)
        except ImportError:
            timestamp = 0.0

        return {
            'recognized': recognized,
            'message': message,
            'details': details or {},
            'timestamp': timestamp
        }
    
    def get_action_keypoints(self, action_type: ActionType, side: TrainingSide) -> List[int]:
        """
        获取动作相关的关键点索引
        
        Args:
            action_type: 动作类型
            side: 训练侧别
            
        Returns:
            List[int]: 关键点索引列表
        """
        try:
            keypoint_key = f"{action_type.upper()}_{side.upper()}"
            
            if hasattr(ActionKeyPoints, keypoint_key):
                return getattr(ActionKeyPoints, keypoint_key)
            else:
                self.logger.warning(f"未找到动作关键点映射: {keypoint_key}")
                return []
                
        except Exception as e:
            self.logger.error(f"获取动作关键点失败: {e}")
            return []
    
    def reset_action_states(self):
        """重置动作状态"""
        self.action_states.clear()
        self.logger.info("动作状态已重置")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict: 统计信息
        """
        success_rate = (self.stats['successful_recognitions'] / self.stats['total_recognitions'] * 100) \
                      if self.stats['total_recognitions'] > 0 else 0.0
        
        return {
            **self.stats,
            'success_rate': success_rate
        }

# 全局动作识别器实例
action_recognizer = ActionRecognizer()
