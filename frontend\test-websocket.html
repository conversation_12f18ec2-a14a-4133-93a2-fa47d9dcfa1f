<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 连接测试</title>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .message-log {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message-item {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #007bff;
            background-color: white;
        }
        .received { border-left-color: #28a745; }
        .sent { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 4px;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>WebSocket 连接测试</h1>
    
    <div class="container">
        <h2>连接控制</h2>
        <input type="text" id="serverUrl" value="http://localhost:5000" placeholder="服务器地址">
        <button onclick="connect()">连接</button>
        <button onclick="disconnect()">断开</button>
        <div id="status" class="status disconnected">未连接</div>
    </div>

    <div class="container">
        <h2>消息发送</h2>
        <button onclick="sendSystemReset()" id="resetBtn" disabled>发送系统重置</button>
        <button onclick="sendTestMessage()" id="testBtn" disabled>发送测试消息</button>
        <br>
        <input type="text" id="customEvent" value="test_message" placeholder="事件名称">
        <input type="text" id="customData" value='{"test": "data"}' placeholder="消息数据 (JSON)">
        <button onclick="sendCustomMessage()" id="customBtn" disabled>发送自定义消息</button>
    </div>

    <div class="container">
        <h2>统计信息</h2>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="receivedCount">0</div>
                <div class="stat-label">接收消息</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="sentCount">0</div>
                <div class="stat-label">发送消息</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="connectionCount">0</div>
                <div class="stat-label">连接次数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="lastMessageTime">无</div>
                <div class="stat-label">最后消息</div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>消息日志</h2>
        <button onclick="clearLog()">清空日志</button>
        <div id="messageLog" class="message-log"></div>
    </div>

    <script>
        let socket = null;
        let stats = {
            received: 0,
            sent: 0,
            connections: 0,
            lastMessageTime: null
        };

        function updateStatus(status, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${status}`;
            statusEl.textContent = message;
        }

        function updateButtons(connected) {
            document.getElementById('resetBtn').disabled = !connected;
            document.getElementById('testBtn').disabled = !connected;
            document.getElementById('customBtn').disabled = !connected;
        }

        function updateStats() {
            document.getElementById('receivedCount').textContent = stats.received;
            document.getElementById('sentCount').textContent = stats.sent;
            document.getElementById('connectionCount').textContent = stats.connections;
            document.getElementById('lastMessageTime').textContent = 
                stats.lastMessageTime ? new Date(stats.lastMessageTime).toLocaleTimeString() : '无';
        }

        function addMessage(type, event, data) {
            const log = document.getElementById('messageLog');
            const messageEl = document.createElement('div');
            messageEl.className = `message-item ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'received' ? '📨' : type === 'sent' ? '📤' : '⚠️';
            
            messageEl.innerHTML = `
                <strong>${icon} ${event}</strong> <span style="float: right; color: #6c757d;">${timestamp}</span><br>
                <pre style="margin: 5px 0; white-space: pre-wrap;">${JSON.stringify(data, null, 2)}</pre>
            `;
            
            log.insertBefore(messageEl, log.firstChild);
            
            // 限制日志数量
            while (log.children.length > 50) {
                log.removeChild(log.lastChild);
            }
            
            stats.lastMessageTime = Date.now();
            updateStats();
        }

        function connect() {
            const url = document.getElementById('serverUrl').value;
            
            if (socket) {
                socket.disconnect();
            }

            updateStatus('connecting', '正在连接...');
            
            socket = io(url, {
                transports: ['websocket', 'polling'],
                timeout: 10000,
                reconnection: true,
                reconnectionAttempts: 5,
                reconnectionDelay: 1000
            });

            socket.on('connect', () => {
                console.log('✅ 已连接到后端服务器');
                updateStatus('connected', `已连接到 ${url} (ID: ${socket.id})`);
                updateButtons(true);
                stats.connections++;
                updateStats();
                addMessage('system', 'connect', { socketId: socket.id });
            });

            socket.on('disconnect', (reason) => {
                console.log('🔌 与后端服务器断开连接:', reason);
                updateStatus('disconnected', `连接断开: ${reason}`);
                updateButtons(false);
                addMessage('system', 'disconnect', { reason });
            });

            socket.on('connect_error', (error) => {
                console.error('❌ 连接错误:', error);
                updateStatus('disconnected', `连接错误: ${error.message}`);
                updateButtons(false);
                addMessage('error', 'connect_error', { error: error.message });
            });

            // 监听所有消息
            socket.onAny((eventName, data) => {
                if (!['connect', 'disconnect', 'connect_error'].includes(eventName)) {
                    console.log(`📨 收到消息 [${eventName}]:`, data);
                    stats.received++;
                    updateStats();
                    addMessage('received', eventName, data);
                }
            });
        }

        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
                updateStatus('disconnected', '已断开连接');
                updateButtons(false);
            }
        }

        function sendSystemReset() {
            if (!socket) return;
            
            const data = {
                timestamp: new Date().toISOString(),
                source: 'websocket_test_page'
            };
            
            socket.emit('system_reset', data);
            stats.sent++;
            updateStats();
            addMessage('sent', 'system_reset', data);
        }

        function sendTestMessage() {
            if (!socket) return;
            
            const data = {
                message: 'Hello from test page!',
                timestamp: new Date().toISOString(),
                source: 'websocket_test_page'
            };
            
            socket.emit('test_message', data);
            stats.sent++;
            updateStats();
            addMessage('sent', 'test_message', data);
        }

        function sendCustomMessage() {
            if (!socket) return;
            
            const event = document.getElementById('customEvent').value;
            const dataStr = document.getElementById('customData').value;
            
            try {
                const data = dataStr ? JSON.parse(dataStr) : {};
                socket.emit(event, data);
                stats.sent++;
                updateStats();
                addMessage('sent', event, data);
            } catch (error) {
                alert('JSON 格式错误: ' + error.message);
            }
        }

        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
            stats.received = 0;
            stats.sent = 0;
            stats.lastMessageTime = null;
            updateStats();
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            updateStats();
            console.log('WebSocket 测试页面已加载');
        };
    </script>
</body>
</html>
