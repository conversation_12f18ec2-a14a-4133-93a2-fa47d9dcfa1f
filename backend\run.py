"""
智能康复系统 - 主启动文件
使用SystemLauncher统一管理系统启动
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from system_launcher import create_launcher

def main():
    """主函数"""
    print("🏥 智能康复系统启动中...")
    
    # 创建系统启动器
    launcher = create_launcher()
    
    # 运行系统
    success = launcher.run()
    
    # 退出
    exit_code = 0 if success else 1
    print(f"系统退出，退出码: {exit_code}")
    sys.exit(exit_code)

if __name__ == '__main__':
    main()