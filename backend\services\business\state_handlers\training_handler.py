"""
智能康复系统 - TRAINING状态处理器
处理动作训练状态的逻辑 - 重构版本
"""
import time
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent, MessageType, TimeConstants
from models.data_models import ZMQDetectData, SystemStateData
from algorithms.action_recognition import action_recognizer
from algorithms.pose_analyzer import pose_analyzer
from algorithms.scoring import action_scorer
from . import BaseStateHandler
from ..user_detection_service import user_detection_service

class TrainingHandler(BaseStateHandler):
    """TRAINING状态处理器 - 重构版本"""
    
    def __init__(self):
        """初始化TRAINING状态处理器"""
        super().__init__(SystemState.TRAINING)
        self.recognition_threshold = 0.8  # 动作识别阈值
        self.min_score_threshold = 60    # 最低分数阈值
        self.user_lost_start_time = None
        self.last_user_id = None
    
    def enter_state(self, context: Dict[str, Any]):
        """进入TRAINING状态"""
        self.logger.info("系统进入动作训练状态")
    
    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理TRAINING状态下的数据 - 持续发送training_message"""
        try:
            # 检查用户状态
            user_status = self._check_user_status(data, context)
            
            # 检查是否需要暂停
            if user_status["should_pause"]:
                return self._handle_pause(user_status, context)
            
            # 处理动作识别和评分
            training_result = self._process_pose_data(data, context)
            
            # 检查是否完成动作或需要切换
            if training_result.get("action_completed"):
                return self._handle_action_completion(data, context, training_result)
            elif training_result.get("action_switch"):
                return self._handle_action_switch(data, context, training_result)
            
            # 发送训练消息
            return self._send_training_message(data, context, training_result)
        

        except Exception as e:
            self.logger.error(f"处理训练数据失败: {e}")
            # 错误情况下也要发送状态数据
            error_state_data = SystemStateData(
                current_state=SystemState.TRAINING,
                message=f"训练数据处理失败: {str(e)}",
                user_info=context.get("user_info"),
                action_list=context.get("action_list", []),
                current_action=context.get("current_action"),
            )

            return {
                "success": False,
                "websocket_message": MessageType.TRAINING_MESSAGE,
                "state_data": error_state_data,
                "message": f"训练数据处理失败: {str(e)}"
            }
    
    def _check_user_status(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """检查用户状态 - 使用公共用户检测服务"""
        # 首先检查用户是否在画面中
        presence_result = user_detection_service.check_user(pose_data, context)

        if presence_result.get("should_pause"):
            return {
                "should_pause": True,
                "pause_reason": presence_result.get("pause_reason"),
                "message": presence_result.get("message")
            }
        return {"should_pause": False}
    
    def _handle_pause(self, user_status: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理暂停逻辑"""
        pause_reason = user_status["pause_reason"]

        if pause_reason == "user_lost":
            event = StateTransitionEvent.USER_LOST
            message_type = MessageType.USER_LOST
        else:  # user_not_auth
            event = StateTransitionEvent.USER_NOT_AUTH
            message_type = MessageType.USER_NOT_AUTH

        # 创建暂停状态数据
        state_data = SystemStateData(
            current_state=SystemState.PAUSE,
            message=user_status["message"],
            user_info=context.get("user_info"),
            action_list=context.get("action_list", []),
            current_action=context.get("current_action"),
        )

        return {
            "success": True,
            "trigger_event": event,
            "next_state": SystemState.PAUSE,
            "websocket_message": message_type,
            "state_data": state_data,
            "message": user_status["message"]
        }
    
    def _process_pose_data(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理姿态数据进行动作识别和评分"""
        try:
            current_action = context.get("current_action")
            if not current_action:
                return {"success": False, "message": "当前任务信息缺失"}
            
            # 进行动作识别
            recognition_result = action_recognizer.recognize_action(
                pose_data.pose_keypoints,
                current_action.action_info.action_type,
                current_action.action_info.side
            )
            pose_quality = pose_analyzer._assess_pose_quality(pose_data.pose_keypoints)
            if not recognition_result.get("recognized", False):
                return {
                    "success": True,
                    "message": "动作未识别",
                    "action_recognized": False,
                    "recognition_details": recognition_result.get("message", ""),
                    "current_score": current_action.score
                }
            
            # 动作识别成功，进行评分
            score_result = action_scorer.score_action(
                current_action.action_info.action_type,
                current_action.action_info.side,
                recognition_result,
                pose_quality
            )
            score = score_result.get("score", 0)
            current_action.score = score
            # 更新反馈消息
            feedback = f"动作得分: {score:.1f}"
            current_action.feedback_messages = [feedback]
            self.logger.info(f"动作识别成功，得分: {score}")
            
            # 简化逻辑：假设每个动作只需要完成一次
            # 检查是否需要切换到下一个动作
            action_list = context.get("action_list", [])
            current_index = context.get("current_action_index", 0)
            
            if current_index + 1 < len(action_list):
                # 还有下一个动作
                return {
                    "success": True,
                    "message": f"动作完成，得分: {score}",
                    "action_recognized": True,
                    "score": score,
                    "action_switch": True
                }
            else:
                # 所有动作完成
                return {
                    "success": True,
                    "message": f"所有动作完成，得分: {score}",
                    "action_recognized": True,
                    "score": score,
                    "action_completed": True
                }
        except Exception as e:
            self.logger.error(f"处理姿态数据失败: {e}")
            return {
                "success": False,
                "message": f"姿态数据处理失败: {str(e)}"
            }
    
    def _handle_action_switch(self, pose_data: ZMQDetectData, context: Dict[str, Any], training_result: Dict[str, Any]) -> Dict[str, Any]:
        """处理动作切换"""
        # 更新动作索引
        current_index = context.get("current_action_index", 0)
        context["current_action_index"] = current_index + 1
        
        # 创建状态数据
        state_data = SystemStateData(
            current_state=SystemState.PREPARATION,
            message="动作完成，准备下一个动作",
            user_info=context.get("user_info"),
            action_list=context.get("action_list", []),
            current_action=context.get("current_action"),
        )
        
        return {
            "success": True,
            "trigger_event": StateTransitionEvent.ACTION_SWITCH,
            "next_state": SystemState.PREPARATION,
            "websocket_message": MessageType.ACTION_SWITCH,
            "state_data": state_data
        }
    
    def _handle_action_completion(self, pose_data: ZMQDetectData, context: Dict[str, Any], training_result: Dict[str, Any]) -> Dict[str, Any]:
        """处理所有动作完成"""
        # 创建状态数据
        state_data = SystemStateData(
            current_state=SystemState.REPORTING,
            message="所有动作完成，生成训练报告",
            user_info=context.get("user_info"),
            action_list=context.get("action_list", []),
            current_action=context.get("current_action"),
        )
        
        return {
            "success": True,
            "trigger_event": StateTransitionEvent.ACTION_COMPLETED,
            "next_state": SystemState.REPORTING,
            "websocket_message": MessageType.ACTION_COMPLETED,
            "state_data": state_data
        }
    
    def _send_training_message(self, pose_data: ZMQDetectData, context: Dict[str, Any], training_result: Dict[str, Any]) -> Dict[str, Any]:
        """发送训练消息"""
        current_action = context.get("current_action")
        
        # 创建状态数据
        state_data = SystemStateData(
            current_state=SystemState.TRAINING,
            message=training_result.get("message", "正在训练中..."),
            user_info=context.get("user_info"),
            action_list=context.get("action_list", []),
            current_action=current_action,
        )
        return {
            "success": True,
            "websocket_message": MessageType.TRAINING_MESSAGE,
            "state_data": state_data,
            "training_result": training_result
        }
    
    def exit_state(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """退出TRAINING状态"""
        self.logger.info("系统退出动作训练状态")
        self.user_lost_start_time = None
        
        current_action = context.get("current_action")
        if current_action:
            current_action.end_time = time.time()
            current_action.action_status = "completed"
        
        training_duration = time.time() - context.get("training_start_time", 0)
        
        return {
            "success": True,
            "message": "动作训练阶段完成",
            "training_duration": training_duration
        }
