"""
智能康复系统 - 数据流监控工具
监控ZMQ → 系统协调器 → WebSocket的数据流
"""
import time
import logging
import threading
from typing import Dict, Any, Optional, Callable
from collections import deque, defaultdict

class DataFlowMonitor:
    """数据流监控器"""
    
    def __init__(self, max_history: int = 1000):
        """初始化数据流监控器"""
        self.logger = logging.getLogger(__name__)
        self.max_history = max_history
        
        # 数据流统计
        self.stats = {
            'zmq_received': 0,
            'pose_data_processed': 0,
            'camera_frames_processed': 0,
            'websocket_sent': 0,
            'errors': 0,
            'start_time': time.time()
        }
        
        # 数据流历史记录
        self.history = {
            'pose_data': deque(maxlen=max_history),
            'camera_frames': deque(maxlen=max_history),
            'websocket_messages': deque(maxlen=max_history),
            'errors': deque(maxlen=max_history)
        }
        
        # 性能指标
        self.performance = {
            'avg_processing_time': 0.0,
            'max_processing_time': 0.0,
            'min_processing_time': float('inf'),
            'last_processing_time': 0.0
        }
        
        # 监控锁
        self.lock = threading.Lock()
        
        # 回调函数
        self.callbacks = defaultdict(list)
        
        self.logger.info("数据流监控器初始化完成")
    
    def record_zmq_data(self, data_type: str, data_size: int = 0, processing_time: float = 0.0):
        """记录ZMQ数据接收"""
        with self.lock:
            timestamp = time.time()
            
            # 更新统计
            self.stats['zmq_received'] += 1
            
            if data_type == 'pose_data':
                self.stats['pose_data_processed'] += 1
                self.history['pose_data'].append({
                    'timestamp': timestamp,
                    'size': data_size,
                    'processing_time': processing_time
                })
            elif data_type == 'camera_frame':
                self.stats['camera_frames_processed'] += 1
                self.history['camera_frames'].append({
                    'timestamp': timestamp,
                    'size': data_size,
                    'processing_time': processing_time
                })
            
            # 更新性能指标
            if processing_time > 0:
                self._update_performance_metrics(processing_time)
            
            # 触发回调
            self._trigger_callbacks('zmq_data', {
                'type': data_type,
                'size': data_size,
                'processing_time': processing_time,
                'timestamp': timestamp
            })
    
    def record_websocket_message(self, message_type: str, message_size: int = 0):
        """记录WebSocket消息发送"""
        with self.lock:
            timestamp = time.time()
            
            # 更新统计
            self.stats['websocket_sent'] += 1
            
            # 记录历史
            self.history['websocket_messages'].append({
                'timestamp': timestamp,
                'type': message_type,
                'size': message_size
            })
            
            # 触发回调
            self._trigger_callbacks('websocket_message', {
                'type': message_type,
                'size': message_size,
                'timestamp': timestamp
            })
    
    def record_error(self, error_type: str, error_message: str, component: str = 'unknown'):
        """记录错误"""
        with self.lock:
            timestamp = time.time()
            
            # 更新统计
            self.stats['errors'] += 1
            
            # 记录历史
            error_record = {
                'timestamp': timestamp,
                'type': error_type,
                'message': error_message,
                'component': component
            }
            self.history['errors'].append(error_record)
            
            # 记录日志
            self.logger.error(f"数据流错误 - {component}: {error_type} - {error_message}")
            
            # 触发回调
            self._trigger_callbacks('error', error_record)
    
    def _update_performance_metrics(self, processing_time: float):
        """更新性能指标"""
        self.performance['last_processing_time'] = processing_time
        
        if processing_time > self.performance['max_processing_time']:
            self.performance['max_processing_time'] = processing_time
        
        if processing_time < self.performance['min_processing_time']:
            self.performance['min_processing_time'] = processing_time
        
        # 计算平均处理时间（简单移动平均）
        total_records = len(self.history['pose_data']) + len(self.history['camera_frames'])
        if total_records > 0:
            total_time = sum(
                record.get('processing_time', 0) 
                for record in list(self.history['pose_data']) + list(self.history['camera_frames'])
            )
            self.performance['avg_processing_time'] = total_time / total_records
    
    def _trigger_callbacks(self, event_type: str, data: Dict[str, Any]):
        """触发回调函数"""
        for callback in self.callbacks[event_type]:
            try:
                callback(data)
            except Exception as e:
                self.logger.error(f"回调函数执行失败: {e}")
    
    def add_callback(self, event_type: str, callback: Callable):
        """添加回调函数"""
        self.callbacks[event_type].append(callback)
        self.logger.debug(f"添加回调函数: {event_type}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self.lock:
            current_time = time.time()
            uptime = current_time - self.stats['start_time']
            
            return {
                'uptime': uptime,
                'total_stats': self.stats.copy(),
                'performance': self.performance.copy(),
                'rates': {
                    'zmq_rate': self.stats['zmq_received'] / uptime if uptime > 0 else 0,
                    'pose_rate': self.stats['pose_data_processed'] / uptime if uptime > 0 else 0,
                    'frame_rate': self.stats['camera_frames_processed'] / uptime if uptime > 0 else 0,
                    'websocket_rate': self.stats['websocket_sent'] / uptime if uptime > 0 else 0
                },
                'history_sizes': {
                    'pose_data': len(self.history['pose_data']),
                    'camera_frames': len(self.history['camera_frames']),
                    'websocket_messages': len(self.history['websocket_messages']),
                    'errors': len(self.history['errors'])
                }
            }
    
    def get_recent_data(self, data_type: str, count: int = 10) -> list:
        """获取最近的数据记录"""
        with self.lock:
            if data_type in self.history:
                return list(self.history[data_type])[-count:]
            return []
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        with self.lock:
            errors = list(self.history['errors'])
            
            if not errors:
                return {'total_errors': 0, 'error_types': {}, 'recent_errors': []}
            
            # 统计错误类型
            error_types = defaultdict(int)
            for error in errors:
                error_types[error['type']] += 1
            
            return {
                'total_errors': len(errors),
                'error_types': dict(error_types),
                'recent_errors': errors[-5:]  # 最近5个错误
            }
    
    def reset_stats(self):
        """重置统计信息"""
        with self.lock:
            self.stats = {
                'zmq_received': 0,
                'pose_data_processed': 0,
                'camera_frames_processed': 0,
                'websocket_sent': 0,
                'errors': 0,
                'start_time': time.time()
            }
            
            # 清空历史记录
            for history_list in self.history.values():
                history_list.clear()
            
            # 重置性能指标
            self.performance = {
                'avg_processing_time': 0.0,
                'max_processing_time': 0.0,
                'min_processing_time': float('inf'),
                'last_processing_time': 0.0
            }
            
            self.logger.info("数据流监控统计已重置")
    
    def check_health(self) -> Dict[str, Any]:
        """检查数据流健康状态"""
        with self.lock:
            current_time = time.time()
            uptime = current_time - self.stats['start_time']
            
            # 检查数据流速率
            pose_rate = self.stats['pose_data_processed'] / uptime if uptime > 0 else 0
            frame_rate = self.stats['camera_frames_processed'] / uptime if uptime > 0 else 0
            error_rate = self.stats['errors'] / uptime if uptime > 0 else 0
            
            # 健康状态判断
            health_status = 'healthy'
            issues = []
            
            if pose_rate < 0.1:  # 姿态数据速率过低
                health_status = 'warning'
                issues.append('姿态数据接收速率过低')
            
            if frame_rate < 1.0:  # 视频帧速率过低
                health_status = 'warning'
                issues.append('视频帧接收速率过低')
            
            if error_rate > 0.1:  # 错误率过高
                health_status = 'error'
                issues.append('错误率过高')
            
            if self.performance['avg_processing_time'] > 1.0:  # 处理时间过长
                health_status = 'warning'
                issues.append('数据处理时间过长')
            
            return {
                'status': health_status,
                'issues': issues,
                'metrics': {
                    'pose_rate': pose_rate,
                    'frame_rate': frame_rate,
                    'error_rate': error_rate,
                    'avg_processing_time': self.performance['avg_processing_time']
                }
            }

# 全局数据流监控器实例
data_flow_monitor = DataFlowMonitor()
