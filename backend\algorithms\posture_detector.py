"""
智能康复系统 - 姿态检测器
实现坐姿检测和准备姿势验证功能
"""
import numpy as np
import time
import logging
from typing import List, Dict, Tuple, Optional, Any
from models.constants import KeyPointMapping


class SittingStateTracker:
    """坐姿状态跟踪器"""
    def __init__(self, required_duration: float = 3.0, max_history_size: int = 50):
        self.required_duration = required_duration
        self.max_history_size = max_history_size
        self.sitting_history: List[Tuple[float, bool]] = []
        self.logger = logging.getLogger(__name__)
        
    def update_sitting_state(self, is_sitting: bool) -> None:
        """更新坐姿状态"""
        current_time = time.time()
        self.sitting_history.append((current_time, is_sitting))
        self._cleanup_old_records(current_time)

    def is_sitting_stable(self) -> bool:
        """检查坐姿是否稳定（持续指定时间）"""
        if len(self.sitting_history) < 5:
            return False
        current_time = time.time()
        stable_start_time = current_time - self.required_duration
        recent_records = [r for r in self.sitting_history if r[0] >= stable_start_time]

        return bool(recent_records) and all(record[1] for record in recent_records)

    def get_sitting_duration(self) -> float:
        """获取当前连续坐姿持续时间"""
        if not self.sitting_history:
            return 0.0

        current_time = time.time()
        for timestamp, is_sitting in reversed(self.sitting_history):
            if is_sitting:
                return current_time - timestamp
            else:
                break
        return 0.0

    def _cleanup_old_records(self, current_time: float) -> None:
        """清理过期的历史记录"""
        cutoff_time = current_time - self.required_duration * 2
        self.sitting_history = [r for r in self.sitting_history if r[0] >= cutoff_time]

        if len(self.sitting_history) > self.max_history_size:
            self.sitting_history = self.sitting_history[-self.max_history_size:]

    def reset(self) -> None:
        """重置状态跟踪器"""
        self.sitting_history.clear()


class PostureDetector:
    """姿态检测器"""
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.confidence_threshold = 0.3
        self.knee_angle_min = 90
        self.knee_angle_max = 130
        self.state_tracker = SittingStateTracker()
        # 动作类型检测方法映射
        self.detection_strategies = {
            "shoulder_touch": self._detect_sitting_posture,
            "arm_raise": self._detect_sitting_posture,
            "finger_touch": self._detect_sitting_posture,
            "palm_flip": self._detect_sitting_posture
        }

    def detect_preparation_posture(self, action_type: str, keypoints: List[List]) -> bool:
        """检测准备姿势"""
        try:
            detection_method = self.detection_strategies.get(action_type, self._detect_sitting_posture)
            is_correct_posture = detection_method(keypoints)
            self.state_tracker.update_sitting_state(is_correct_posture)
            return is_correct_posture
        except Exception as e:
            self.logger.error(f"准备姿势检测失败: {e}")
            return False

    def _detect_sitting_posture(self, keypoints: List[List]) -> bool:
        """检测坐姿"""
        try:
            if not self._validate_input_data(keypoints):
                return False
            critical_points = self._extract_sitting_keypoints(keypoints)
            if not critical_points or not self._validate_keypoint_confidence(critical_points):
                return False
            # 分析几何特征
            hip_knee_valid = self._analyze_hip_knee_position(critical_points)
            knee_angle_valid = self._analyze_knee_angle(critical_points)
            torso_valid = self._analyze_torso_alignment(critical_points)

            return hip_knee_valid and knee_angle_valid and torso_valid
        except Exception as e:
            self.logger.error(f"坐姿检测异常: {e}")
            return False

    def _validate_input_data(self, keypoints: List[List]) -> bool:
        """验证输入数据"""
        return bool(keypoints) and len(keypoints) == 133

    def _extract_sitting_keypoints(self, keypoints: List[List]) -> Optional[Dict[str, List]]:
        """提取坐姿检测所需的关键点"""
        try:
            return {
                'left_hip': keypoints[KeyPointMapping.LEFT_HIP],
                'right_hip': keypoints[KeyPointMapping.RIGHT_HIP],
                'left_knee': keypoints[KeyPointMapping.LEFT_KNEE],
                'right_knee': keypoints[KeyPointMapping.RIGHT_KNEE],
                'left_shoulder': keypoints[KeyPointMapping.LEFT_SHOULDER],
                'right_shoulder': keypoints[KeyPointMapping.RIGHT_SHOULDER],
                'left_ankle': keypoints[KeyPointMapping.LEFT_ANKLE],
                'right_ankle': keypoints[KeyPointMapping.RIGHT_ANKLE]
            }
        except (IndexError, KeyError):
            return None

    def _validate_keypoint_confidence(self, critical_points: Dict[str, List]) -> bool:
        """验证关键点置信度"""
        required_points = ['left_hip', 'right_hip', 'left_knee', 'right_knee']
        for point_name in required_points:
            point = critical_points.get(point_name)
            if not point or len(point) < 3 or point[2] < self.confidence_threshold:
                return False
        return True
    
    def _analyze_hip_knee_position(self, critical_points: Dict[str, List]) -> bool:
        """分析臀部和膝盖的位置关系"""
        try:
            # 获取臀部和膝盖的Y坐标
            avg_hip_y = (critical_points['left_hip'][1] + critical_points['right_hip'][1]) / 2
            avg_knee_y = (critical_points['left_knee'][1] + critical_points['right_knee'][1]) / 2

            # 坐姿时，臀部应该在膝盖上方且有合理差异
            return avg_hip_y < avg_knee_y and abs(avg_hip_y - avg_knee_y) > 20
        except Exception:
            return False

    def _analyze_knee_angle(self, critical_points: Dict[str, List]) -> bool:
        """分析膝盖弯曲角度"""
        try:
            left_angle = self._calculate_angle_between_points(
                critical_points['left_hip'], critical_points['left_knee'], critical_points['left_ankle'])
            right_angle = self._calculate_angle_between_points(
                critical_points['right_hip'], critical_points['right_knee'], critical_points['right_ankle'])

            left_valid = self.knee_angle_min <= left_angle <= self.knee_angle_max
            right_valid = self.knee_angle_min <= right_angle <= self.knee_angle_max
            return left_valid or right_valid
        except Exception:
            return False

    def _analyze_torso_alignment(self, critical_points: Dict[str, List]) -> bool:
        """分析躯干对齐情况"""
        try:
            # 计算肩膀和臀部中点
            shoulder_center = [(critical_points['left_shoulder'][0] + critical_points['right_shoulder'][0]) / 2,
                             (critical_points['left_shoulder'][1] + critical_points['right_shoulder'][1]) / 2]
            hip_center = [(critical_points['left_hip'][0] + critical_points['right_hip'][0]) / 2,
                         (critical_points['left_hip'][1] + critical_points['right_hip'][1]) / 2]

            # 计算躯干倾斜角度
            dx = shoulder_center[0] - hip_center[0]
            dy = shoulder_center[1] - hip_center[1]

            if dy == 0:
                return False

            angle = abs(np.degrees(np.arctan(dx / dy)))
            return angle < 30  # 躯干相对直立
        except Exception:
            return False

    def _calculate_angle_between_points(self, p1: List, p2: List, p3: List) -> float:
        """计算三个点形成的角度"""
        try:
            v1 = np.array([p1[0] - p2[0], p1[1] - p2[1]])
            v2 = np.array([p3[0] - p2[0], p3[1] - p2[1]])

            cos_angle = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            cos_angle = np.clip(cos_angle, -1.0, 1.0)

            return np.degrees(np.arccos(cos_angle))
        except Exception:
            return 0.0

    def is_posture_stable(self) -> bool:
        """检查姿势是否稳定"""
        return self.state_tracker.is_sitting_stable()

    def get_posture_feedback(self) -> Dict[str, Any]:
        """获取姿势反馈信息"""
        duration = self.state_tracker.get_sitting_duration()
        return {
            'is_stable': self.state_tracker.is_sitting_stable(),
            'sitting_duration': duration,
            'required_duration': self.state_tracker.required_duration,
            'progress': min(1.0, duration / self.state_tracker.required_duration)
        }

    def reset_detection_state(self) -> None:
        """重置检测状态"""
        self.state_tracker.reset()


# 全局姿态检测器实例
posture_detector = PostureDetector()
