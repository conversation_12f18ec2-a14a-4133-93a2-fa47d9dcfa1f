"""
测试WebSocket消息发送流程
验证从用户检测到前端消息接收的完整流程
"""
import unittest
from unittest.mock import Mock, patch, MagicMock
import time
from models.system_states import SystemState, StateTransitionEvent, MessageType
from models.data_models import ZMQDetectData
from services.business.state_handlers.waiting_handler import Waiting<PERSON>andler
from services.business.system_coordinator import SystemCoordinator

class TestWebSocketFlow(unittest.TestCase):
    """测试WebSocket消息发送流程"""
    
    def setUp(self):
        """设置测试环境"""
        self.waiting_handler = WaitingHandler()
        self.system_coordinator = SystemCoordinator()
        
        # 模拟WebSocket处理器
        self.mock_websocket_handler = Mock()
        self.system_coordinator.websocket_handler = self.mock_websocket_handler
        
    def test_user_detection_flow(self):
        """测试用户检测流程"""
        # 创建模拟的姿态数据
        pose_data = ZMQDetectData(
            timestamp=time.time(),
            patient_id="test_user_001",
            pose_keypoints=[[0, 0, 1.0]] * 133,  # 133个关键点
            pose_bbox=[100, 100, 200, 200],
            face_bbox=[120, 120, 180, 180]
        )
        
        context = {"current_time": time.time()}
        
        # 模拟连续检测3次
        for i in range(3):
            result = self.waiting_handler._handle_pose_detection(pose_data, context)
            
            if i < 2:
                # 前两次检测应该返回检测进度
                self.assertTrue(result["success"])
                self.assertFalse(result["user_detected"])
                self.assertEqual(result["detection_progress"], (i + 1) / 3)
            else:
                # 第三次检测应该触发状态转换
                self.assertTrue(result["success"])
                self.assertTrue(result["user_detected"])
                self.assertEqual(result["trigger_event"], StateTransitionEvent.USER_DETECTED)
                self.assertEqual(result["websocket_message"], MessageType.USER_DETECTED)
                self.assertEqual(result["patient_id"], "test_user_001")
                
    def test_websocket_message_broadcast(self):
        """测试WebSocket消息广播"""
        # 模拟检测成功的结果
        result = {
            "success": True,
            "user_detected": True,
            "patient_id": "test_user_001",
            "detection_timestamp": time.time(),
            "websocket_message": MessageType.USER_DETECTED,
            "message": "检测到用户: test_user_001"
        }
        
        # 调用广播方法
        self.system_coordinator._broadcast_data_result("pose_data", result)
        
        # 验证WebSocket消息是否被发送
        self.mock_websocket_handler.broadcast_message.assert_called()
        
        # 获取调用参数
        call_args = self.mock_websocket_handler.broadcast_message.call_args
        message_type = call_args[0][0]
        message_data = call_args[0][1]
        
        # 验证消息类型和数据
        self.assertEqual(message_type, MessageType.USER_DETECTED)
        self.assertEqual(message_data["patient_id"], "test_user_001")
        self.assertEqual(message_data["confidence"], 1.0)
        self.assertIn("detection_timestamp", message_data)
        
    def test_invalid_patient_id_handling(self):
        """测试无效patient_id的处理"""
        # 测试空patient_id
        pose_data = ZMQDetectData(
            timestamp=time.time(),
            patient_id="",
            pose_keypoints=[[0, 0, 1.0]] * 133,
            pose_bbox=[100, 100, 200, 200],
            face_bbox=[120, 120, 180, 180]
        )
        
        context = {"current_time": time.time()}
        result = self.waiting_handler._handle_pose_detection(pose_data, context)
        
        self.assertTrue(result["success"])
        self.assertFalse(result["user_detected"])
        self.assertEqual(result["message"], "未检测到有效用户")
        
        # 测试"unknown" patient_id
        pose_data.patient_id = "unknown"
        result = self.waiting_handler._handle_pose_detection(pose_data, context)
        
        self.assertTrue(result["success"])
        self.assertFalse(result["user_detected"])
        self.assertEqual(result["message"], "未检测到有效用户")
        
    def test_detection_reset_on_different_user(self):
        """测试不同用户时检测计数重置"""
        context = {"current_time": time.time()}
        
        # 第一个用户检测2次
        pose_data1 = ZMQDetectData(
            timestamp=time.time(),
            patient_id="user_001",
            pose_keypoints=[[0, 0, 1.0]] * 133,
            pose_bbox=[100, 100, 200, 200],
            face_bbox=[120, 120, 180, 180]
        )
        
        for i in range(2):
            result = self.waiting_handler._handle_pose_detection(pose_data1, context)
            self.assertFalse(result["user_detected"])
            
        # 切换到第二个用户
        pose_data2 = ZMQDetectData(
            timestamp=time.time(),
            patient_id="user_002",
            pose_keypoints=[[0, 0, 1.0]] * 133,
            pose_bbox=[100, 100, 200, 200],
            face_bbox=[120, 120, 180, 180]
        )
        
        result = self.waiting_handler._handle_pose_detection(pose_data2, context)
        
        # 检测计数应该重置为1
        self.assertEqual(result["detection_progress"], 1/3)
        self.assertEqual(self.waiting_handler.detection_count, 1)
        self.assertEqual(self.waiting_handler.last_patient_id, "user_002")

if __name__ == '__main__':
    unittest.main()
