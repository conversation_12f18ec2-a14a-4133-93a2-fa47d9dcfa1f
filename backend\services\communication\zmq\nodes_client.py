from .zmq_receiver import zmq_receiver
# app/tasks/nodes_handler.py
import threading
import logging
import numpy as np
from .zmq_receiver import zmq_receiver
from ...business.system_coordinator import system_coordinator
from ...business.state_manager import state_manager
logger = logging.getLogger(__name__)
background_thread = None
is_running = False
thread_lock = threading.Lock()
from models.data_models import SystemStateData, PoseDataConverter,ZMQDetectData
logger = logging.getLogger(__name__)
def start_background_services():
    global is_running
    logger.info("后台数据处理与发送任务已启动...")
    while is_running:
        try:
            # 1. 获取原始数据
            zmq_receiver.get_latest_data()
            detect_data = zmq_receiver.latest_detect_data
            frame_data = zmq_receiver.latest_camera_frame
            if isinstance(detect_data, dict):
                # 如果是字典格式，转换为ZMQDetectData对象
                zmq_detect_obj = PoseDataConverter.convert_zmq_detect_data(detect_data)
                if zmq_detect_obj is None:
                    logger.warning(f"ZMQ数据转换失败，跳过处理: {detect_data}")
                    return
                processed_data = zmq_detect_obj
            # 2. 调用业务层处理，并获取返回的数据包
            # [修改] data_packet 现在可能为 None
            system_coordinator.handle_zmq_data(frame_data,processed_data)

        except Exception as e:
            logger.error(f"unified_data_handler 循环出错: {e}", exc_info=True)
        
    logger.info("后台数据处理与发送任务已优雅停止。")

# start/stop/is_running 函数保持不变
def start_background_task():
    global is_running, background_thread
    with thread_lock:
        if not is_running:
            if zmq_receiver.initialize():
                is_running = True
                state_manager.reset_to_idle()
                background_thread = threading.Thread(target=start_background_services)
                background_thread.daemon = True
                background_thread.start()
                logger.info("后台任务线程已成功启动。")
                return True
            else:
                logger.error("无法连接到nodes服务，后台任务启动失败。")
                return False
    return True
def stop_background_task():
    global is_running
    with thread_lock:
        if is_running:
            is_running = False
            if background_thread and background_thread.is_alive():
                background_thread.join(timeout=1.0)
            zmq_receiver.cleanup()
            logger.info("后台任务已优雅停止。")

def is_task_running():
    return is_running