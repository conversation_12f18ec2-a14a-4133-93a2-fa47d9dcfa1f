# WebSocket消息格式规范

## 概述

本文档定义了智能康复系统中前后端WebSocket通信的统一消息格式规范。系统采用Socket.IO进行实时双向通信，所有消息都遵循标准化的数据结构，确保前后端数据交互的一致性和可靠性。

## 核心设计原则

### 1. 统一消息格式
- 所有WebSocket消息都采用标准的JSON格式
- 包含必要的元数据字段（消息类型、时间戳、会话ID等）
- 数据字段统一使用SystemStateData结构

### 2. 类型安全
- 使用枚举定义消息类型，避免字符串硬编码
- 严格的数据验证和序列化机制
- 支持数据结构的版本兼容性

### 3. 实时性保证
- 支持高频率的实时数据传输（姿态关键点、摄像头帧）
- 消息优先级机制，确保关键状态消息及时传递
- 连接断开重连和消息队列机制

## 统一消息格式

### 基本消息结构

所有WebSocket消息都遵循以下统一格式：

```json
{
  "message_type": "string",     // 消息类型（必需）
  "session_id": "string",       // 会话ID（可选）
  "timestamp": 1234567890.123,  // 时间戳（必需）
  "data": {}                    // 消息数据（必需，SystemStateData格式）
}
```

### 字段详细说明

- **message_type**: 消息类型标识符，使用MessageType枚举值
  - 类型：String
  - 必需：是
  - 示例：`"waiting_message"`, `"login_success"`, `"training_message"`

- **session_id**: 唯一会话标识符
  - 类型：String (UUID格式)
  - 必需：否（某些系统级消息可能不包含）
  - 用途：跟踪客户端会话，支持多客户端管理

- **timestamp**: 消息生成时间戳
  - 类型：Number (Unix时间戳，秒，包含小数部分)
  - 必需：是
  - 用途：消息排序、延迟分析、调试

- **data**: 具体的消息数据
  - 类型：Object (SystemStateData结构)
  - 必需：是
  - 内容：根据消息类型包含不同的业务数据

## 消息类型定义

### 后端到前端消息类型

#### 1. 系统初始化消息 (system_init)

**用途**: WebSocket连接建立后的系统初始化通知

**触发时机**: 前端连接到后端时自动发送

**消息格式**:
```json
{
  "message_type": "system_init",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "current_state": "WAITING",
    "message": "系统初始化完成，等待用户识别...",
    "user_info": null,
    "current_action": null,
    "action_list": [],
    "pose_keypoints": [],
    "frame_data": null
  }
}
```

#### 2. 等待状态消息 (waiting_message)

**用途**: 持续发送等待用户识别的状态信息

**触发时机**: 系统处于WAITING状态时持续发送

**消息格式**:
```json
{
  "message_type": "waiting_message",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "current_state": "WAITING",
    "message": "请站在摄像头前进行身份识别...",
    "user_info": null,
    "current_action": null,
    "action_list": [],
    "pose_keypoints": [[x1,y1,c1], [x2,y2,c2], ...],  // 实时姿态数据
    "frame_data": "base64_encoded_image_data"           // 实时摄像头帧
  }
}
```

#### 3. 登录成功消息 (login_success)

**用途**: 通知前端用户认证成功，系统进入介绍阶段

**触发时机**: 用户检测和认证成功后发送

**消息格式**:
```json
{
  "message_type": "login_success",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "current_state": "INTRODUCTION",
    "message": "欢迎回来，张三！",
    "user_info": {
      "patient_id": "user_001",
      "name": "张三",
      "age": 65,
      "gender": "male",
      "last_login": "2023-12-21T10:30:45.123Z"
    },
    "current_action": null,
    "action_list": [
      {
        "action_type": "shoulder_touch",
        "side": "left",
        "video_url":"https://example.com/video1.mp4",
     
        "required_keypoints": [11, 12, 13, 14, 15, 16]
      },
      {
        "action_type": "arm_raise",
        "side": "right",
        "video_url":"https://example.com/video2.mp4",
        "required_keypoints": [11, 12, 13, 14, 15, 16]
      }
    ],
    "pose_keypoints": [[x1,y1,c1], [x2,y2,c2], ...],
    "frame_data": "base64_encoded_image_data"
  }
}
```

#### 4. 介绍时钟结束消息 (clock_introduction_finish)

**用途**: 通知前端介绍阶段倒计时结束，进入动作准备阶段

**触发时机**: 前端发送preparing_finish命令后

**消息格式**:
```json
{
  "message_type": "clock_introduction_finish",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "current_state": "PREPARATION",
    "message": "任务介绍完成，开始播放动作引导视频...",
    "user_info": {
      "patient_id": "user_001",
      "name": "张三"
    },
    "current_action": {
      "action_type": "shoulder_touch",
      "side": "left",
      "sets": 3,
      "video_url":"https://example.com/video1.mp4",
    },
    "action_list": [...],
    "pose_keypoints": [[x1,y1,c1], [x2,y2,c2], ...],
    "frame_data": "base64_encoded_image_data"
  }
}
```

#### 5. 训练消息 (training_message)

**用途**: 发送训练过程中的实时信息和反馈

**触发时机**: 系统处于TRAINING状态时持续发送

**消息格式**:
```json
{
  "message_type": "training_message",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "current_state": "TRAINING",
    "message": "动作标准，继续保持！",
    "user_info": {
      "patient_id": "user_001",
      "name": "张三"
    },
    "current_action": {
      "action_type": "shoulder_touch",
      "side": "left",
      "sets": 3,
      "reps_per_set": 10,
      "target_score": 80,
      "current_set": 2,
      "current_rep": 5,
      "current_score": 85,
      "feedback": "动作标准，继续保持！",
      "completion_rate": 0.5
    },
    "action_list": [...],
    "pose_keypoints": [[x1,y1,c1], [x2,y2,c2], ...],
    "frame_data": "base64_encoded_image_data",
    "progress_info": {
      "total_actions": 4,
      "completed_actions": 1,
      "current_action_progress": 0.5,
      "overall_progress": 0.375
    }
  }
}
```

#### 6. 动作切换消息 (action_switch)

**用途**: 通知前端切换到下一个动作

**触发时机**: 当前动作完成，切换到下一个动作时

**消息格式**:
```json
{
  "message_type": "action_switch",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "current_state": "PREPARATION",
    "message": "切换到新动作: 手臂上抬",
    "user_info": {
      "patient_id": "user_001",
      "name": "张三"
    },
    "current_action": {
      "action_type": "arm_raise",
      "side": "right",
      "sets": 2,
      "reps_per_set": 8,
      "target_score": 75,
      "current_set": 1,
      "current_rep": 0,
      "current_score": 0
    },
    "action_list": [...],
    "pose_keypoints": [[x1,y1,c1], [x2,y2,c2], ...],
    "frame_data": "base64_encoded_image_data",
    "progress_info": {
      "total_actions": 4,
      "completed_actions": 2,
      "current_action_progress": 0.0,
      "overall_progress": 0.5
    }
  }
}
```

#### 7. 动作完成消息 (action_completed)

**用途**: 通知前端所有动作完成，进入报告阶段

**触发时机**: 所有动作训练完成后

**消息格式**:
```json
{
  "message_type": "action_completed",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "current_state": "REPORTING",
    "message": "所有动作完成！正在生成训练报告...",
    "user_info": {
      "patient_id": "user_001",
      "name": "张三"
    },
    "current_action": null,
    "action_list": [...],
    "pose_keypoints": [[x1,y1,c1], [x2,y2,c2], ...],
    "frame_data": "base64_encoded_image_data",
    "progress_info": {
      "total_actions": 4,
      "completed_actions": 4,
      "current_action_progress": 1.0,
      "overall_progress": 1.0,
      "session_summary": {
        "total_time": 1800,
        "average_score": 82.5,
        "completion_rate": 1.0
      }
    }
  }
}
```

#### 8. 用户丢失消息 (user_lost)

**用途**: 通知前端用户离开摄像头范围，系统进入暂停状态

**触发时机**: 连续3秒检测不到用户时

**消息格式**:
```json
{
  "message_type": "user_lost",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "current_state": "PAUSE",
    "message": "检测不到用户，训练已暂停。请返回摄像头前继续训练。",
    "user_info": {
      "patient_id": "user_001",
      "name": "张三"
    },
    "current_action": {
      "action_type": "shoulder_touch",
      "side": "left",
      "paused_at_set": 2,
      "paused_at_rep": 3,
      "paused_score": 78
    },
    "action_list": [...],
    "pose_keypoints": [],
    "frame_data": "base64_encoded_image_data",
    "pause_info": {
      "pause_reason": "user_lost",
      "paused_from_state": "TRAINING",
      "pause_timestamp": **********.789
    }
  }
}
```

#### 9. 用户未认证消息 (user_not_auth)

**用途**: 通知前端检测到非登录用户，系统进入暂停状态

**触发时机**: 检测到与登录用户不同的人员时

**消息格式**:
```json
{
  "message_type": "user_not_auth",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "current_state": "PAUSE",
    "message": "检测到非授权用户，训练已暂停。请原用户返回继续训练。",
    "user_info": {
      "patient_id": "user_001",
      "name": "张三"
    },
    "current_action": {...},
    "action_list": [...],
    "pose_keypoints": [[x1,y1,c1], [x2,y2,c2], ...],
    "frame_data": "base64_encoded_image_data",
    "pause_info": {
      "pause_reason": "user_not_auth",
      "paused_from_state": "TRAINING",
      "pause_timestamp": **********.789,
      "detected_user_id": "unknown_user"
    }
  }
}
```

#### 10. 用户返回消息 (user_back)

**用途**: 通知前端用户返回，系统恢复到暂停前的状态

**触发时机**: 暂停状态下检测到原用户返回时

**消息格式**:
```json
{
  "message_type": "user_back",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "current_state": "TRAINING",
    "message": "欢迎回来！训练已恢复。",
    "user_info": {
      "patient_id": "user_001",
      "name": "张三"
    },
    "current_action": {
      "action_type": "shoulder_touch",
      "side": "left",
      "current_set": 2,
      "current_rep": 3,
      "current_score": 78
    },
    "action_list": [...],
    "pose_keypoints": [[x1,y1,c1], [x2,y2,c2], ...],
    "frame_data": "base64_encoded_image_data",
    "resume_info": {
      "resumed_from_state": "PAUSE",
      "pause_duration": 45.5,
      "resume_timestamp": **********.289
    }
  }
}
```

#### 11. 报告时钟结束消息 (clock_reporting_finish)

**用途**: 通知前端报告展示时间结束，系统返回等待状态

**触发时机**: 报告展示超时或用户确认后

**消息格式**:
```json
{
  "message_type": "clock_reporting_finish",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "current_state": "WAITING",
    "message": "训练报告展示完成，系统返回等待状态。",
    "user_info": null,
    "current_action": null,
    "action_list": [],
    "pose_keypoints": [],
    "frame_data": null
  }
}
```

### 前端到后端消息类型

#### 1. 客户端命令 (client_command)

**用途**: 前端向后端发送各种控制命令

**触发时机**: 用户操作或前端状态变化时

**消息格式**:
```json
{
  "message_type": "client_command",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {
    "command_type": "preparing_finish",  // 命令类型
    "command_data": {                    // 命令数据
      "user_id": "user_001",
      "current_state": "INTRODUCTION"
    }
  }
}
```

**支持的命令类型**:
- `preparing_finish`: 准备阶段完成，请求进入训练状态
- `pause_request`: 用户主动请求暂停
- `resume_request`: 用户请求恢复训练
- `skip_action`: 跳过当前动作
- `restart_session`: 重新开始训练会话

## 核心数据结构

### SystemStateData结构

所有WebSocket消息的data字段都使用SystemStateData结构，确保数据格式的一致性：

```python
@dataclass
class SystemStateData:
    """系统状态数据结构"""
    current_state: SystemState                    # 当前系统状态
    message: str = ""                            # 状态消息
    user_info: Optional[UserInfo] = None         # 用户信息
    current_action: Optional[CurrentAction] = None  # 当前动作信息
    action_list: List[ActionInfo] = field(default_factory=list)  # 动作列表
    pose_keypoints: List[List[float]] = field(default_factory=list)  # 姿态关键点
    frame_data: Optional[str] = None             # 摄像头帧数据(base64)
    progress_info: Optional[Dict[str, Any]] = None  # 进度信息
    statistics: Optional[Dict[str, Any]] = None  # 统计信息
    session_id: Optional[str] = None             # 会话ID
    state_history: List[str] = field(default_factory=list)  # 状态历史
```

### UserInfo结构

```python
@dataclass
class UserInfo:
    """用户信息结构"""
    patient_id: str                              # 患者ID
    name: str                                    # 姓名
    age: Optional[int] = None                    # 年龄
    gender: Optional[str] = None                 # 性别
    last_login: Optional[datetime] = None        # 最后登录时间
```

### CurrentAction结构

```python
@dataclass
class CurrentAction:
    """当前动作信息结构"""
    action_type: ActionType                      # 动作类型
    side: TrainingSide                          # 训练侧别
    sets: int                                   # 总组数
    reps_per_set: int                          # 每组重复次数
    target_score: float                        # 目标分数
    current_set: int = 1                       # 当前组数
    current_rep: int = 0                       # 当前重复次数
    current_score: float = 0.0                 # 当前分数
    feedback: str = ""                         # 实时反馈
    completion_rate: float = 0.0               # 完成率
    required_keypoints: List[int] = field(default_factory=list)  # 需要的关键点
```

### 姿态关键点格式

系统使用RTMPose模型，包含133个关键点，每个关键点为三元组格式：

```json
[
  [x1, y1, confidence1],  // 关键点1: 鼻尖
  [x2, y2, confidence2],  // 关键点2: 左眼
  [x3, y3, confidence3],  // 关键点3: 右眼
  ...                     // 共133个关键点
]
```

**关键点说明**:
- `x, y`: 关键点在图像中的像素坐标
- `confidence`: 检测置信度 (0.0-1.0)
- 坐标系：左上角为原点(0,0)，x轴向右，y轴向下

## 实现细节

### 后端实现

#### 消息发送器

```python
class WebSocketManager:
    def send_state_message(self, message_type: MessageType,
                          state_data: SystemStateData,
                          target_client_id: Optional[str] = None):
        """发送状态消息"""
        try:
            # 序列化消息数据
            message_data = DataSerializer.websocket_message_to_dict(state_data)

            # 使用Flask应用上下文发送消息
            if self.app:
                with self.app.app_context():
                    self.socketio.emit(message_type.value, message_data, namespace='/')
                    self.stats['messages_sent'] += 1

        except Exception as e:
            self.logger.error(f"发送WebSocket消息失败: {e}")
```

#### 数据序列化

```python
class DataSerializer:
    @staticmethod
    def websocket_message_to_dict(message: Any) -> Dict:
        """WebSocket消息序列化"""
        if hasattr(message, '__dict__'):
            result = {}
            for key, value in message.__dict__.items():
                if value is not None:
                    if hasattr(value, '__dict__'):
                        result[key] = DataSerializer._serialize_data(value)
                    elif isinstance(value, list):
                        result[key] = [DataSerializer._serialize_data(item) for item in value]
                    else:
                        result[key] = value
            return result
        return message
```

### 前端实现

#### 消息处理器

```javascript
class WebSocketService {
  _handleMessage(messageType, data) {
    try {
      // 构造标准消息格式
      const message = {
        message_type: messageType,
        data: data,
        timestamp: Date.now()
      }

      // 分发到主store的统一消息处理器
      if (this.store && this.store.handleMessage) {
        this.store.handleMessage(message)
      }
    } catch (error) {
      console.error('处理WebSocket消息失败:', error)
    }
  }
}
```

#### 状态管理

```javascript
// Pinia Store中的消息处理
const handleMessage = (message) => {
  const { message_type, data } = message

  // 更新实时数据
  if (data.frame_data) {
    frameData.value = data.frame_data
    frameCount.value += 1
  }

  if (data.pose_keypoints) {
    poseKeypoints.value = data.pose_keypoints
  }

  // 根据消息类型处理业务逻辑
  switch (message_type) {
    case 'waiting_message':
      handleWaitingMessage(data)
      break
    case 'login_success':
      handleLoginSuccess(data)
      break
    // ... 其他消息类型处理
  }
}
```

## 最佳实践

### 1. 消息频率控制

**实时数据消息** (waiting_message, training_message):
- 发送频率：约30-60 FPS
- 优化策略：仅在数据变化时发送，避免重复消息

**状态变化消息** (login_success, action_switch):
- 发送频率：事件驱动
- 确保消息的及时性和可靠性

### 2. 数据压缩优化

**摄像头帧数据**:
```python
def _encode_frame(self, camera_frame):
    """优化的帧数据编码"""
    if camera_frame and 'frame_data' in camera_frame:
        # 压缩JPEG质量以减少数据量
        return camera_frame['frame_data']  # 已经是压缩后的hex编码
    return None
```

**姿态关键点数据**:
```python
def _optimize_keypoints(self, keypoints):
    """优化关键点数据精度"""
    if not keypoints:
        return []

    # 保留2位小数精度，减少数据量
    return [[round(x, 2), round(y, 2), round(c, 2)] for x, y, c in keypoints]
```

### 3. 错误处理机制

**连接断开处理**:
```javascript
// 前端重连机制
socket.on('disconnect', (reason) => {
  console.log('WebSocket连接断开:', reason)

  // 自动重连
  if (this.reconnectAttempts < this.maxReconnectAttempts) {
    setTimeout(() => {
      this.connect()
      this.reconnectAttempts++
    }, this.reconnectInterval)
  }
})
```

**消息验证**:
```python
def validate_websocket_message(data: Dict) -> bool:
    """验证WebSocket消息格式"""
    required_fields = ['message_type', 'timestamp', 'data']

    if not all(field in data for field in required_fields):
        return False

    # 验证消息类型
    if data['message_type'] not in [mt.value for mt in MessageType]:
        return False

    return True
```

### 4. 性能监控

**消息统计**:
```python
class WebSocketManager:
    def __init__(self):
        self.stats = {
            'messages_sent': 0,
            'messages_received': 0,
            'connection_count': 0,
            'error_count': 0
        }

    def get_stats(self):
        """获取WebSocket统计信息"""
        return {
            **self.stats,
            'current_connections': len(self.connected_clients),
            'uptime': time.time() - self.start_time
        }
```

## 故障排除

### 常见问题

1. **消息丢失**
   - 原因：网络不稳定或消息发送过快
   - 解决：实现消息确认机制和重发机制

2. **数据格式错误**
   - 原因：前后端数据结构不一致
   - 解决：使用严格的数据验证和版本控制

3. **连接频繁断开**
   - 原因：网络环境不稳定或服务器负载过高
   - 解决：优化重连策略和服务器性能

### 调试工具

**后端日志**:
```python
# 启用详细的WebSocket日志
logging.getLogger('socketio').setLevel(logging.DEBUG)
logging.getLogger('engineio').setLevel(logging.DEBUG)
```

**前端调试**:
```javascript
// 启用Socket.IO调试模式
localStorage.debug = 'socket.io-client:socket'
```

## 版本兼容性

### 消息格式版本控制

```json
{
  "message_type": "training_message",
  "version": "1.0",
  "session_id": "550e8400-e29b-41d4-a716-************",
  "timestamp": **********.789,
  "data": {...}
}
```

### 向后兼容策略

1. **新增字段**：使用可选字段，保持向后兼容
2. **字段重命名**：保留旧字段名，同时支持新字段名
3. **数据类型变更**：提供数据转换函数

## 安全考虑

### 1. 数据验证
- 严格验证所有输入数据
- 防止XSS和注入攻击
- 限制消息大小和频率

### 2. 身份认证
- 使用session_id进行会话管理
- 实现用户身份验证机制
- 防止未授权访问

### 3. 数据加密
- 在生产环境中使用WSS (WebSocket Secure)
- 敏感数据传输加密
- 实现消息完整性校验

---

*本文档基于智能康复系统v3.0实现，最后更新时间：2023-12-21*