# 状态处理器架构设计文档

## 概述

智能康复系统采用基于状态机的架构设计，通过状态处理器模式实现复杂的业务逻辑管理。本文档详细描述了状态机的设计原理、状态处理器的实现架构、状态转换规则以及验证机制。

## 核心设计理念

### 1. 状态机驱动架构
- **单一状态原则**：系统在任何时刻只能处于一个明确的状态
- **确定性转换**：状态转换必须基于明确的事件和条件
- **状态封装**：每个状态的业务逻辑完全封装在对应的处理器中
- **可预测性**：系统行为完全可预测和可追踪

### 2. 处理器模式
- **职责分离**：每个状态处理器只负责特定状态的业务逻辑
- **统一接口**：所有处理器实现相同的基础接口
- **可扩展性**：新增状态只需实现新的处理器
- **可测试性**：每个处理器可独立测试

### 3. 事件驱动机制
- **异步处理**：支持异步事件处理和状态转换
- **事件队列**：确保事件按顺序处理
- **回调机制**：支持状态变化的回调通知
- **错误恢复**：完善的错误处理和状态恢复机制

## 系统状态定义

### 状态枚举

```python
class SystemState(str, Enum):
    """系统状态枚举"""
    WAITING = "waiting"                    # 等待用户识别和登录
    INTRODUCTION = "introduction"          # 欢迎和任务介绍
    PREPARATION = "preparation"            # 动作准备阶段
    TRAINING = "training"                  # 动作训练阶段
    REPORTING = "reporting"                # 训练报告展示
    PAUSE = "pause"                        # 系统暂停状态
```

### 状态生命周期

```mermaid
stateDiagram-v2
    [*] --> WAITING : 系统启动
    WAITING --> INTRODUCTION : 用户登录成功
    INTRODUCTION --> PREPARATION : 介绍完成
    PREPARATION --> TRAINING : 准备完成
    TRAINING --> PREPARATION : 动作切换
    TRAINING --> REPORTING : 所有动作完成
    REPORTING --> WAITING : 报告展示完成

    INTRODUCTION --> PAUSE : 用户丢失/未认证
    PREPARATION --> PAUSE : 用户丢失/未认证
    TRAINING --> PAUSE : 用户丢失/未认证
    PAUSE --> INTRODUCTION : 用户返回(从介绍阶段)
    PAUSE --> PREPARATION : 用户返回(从准备阶段)
    PAUSE --> TRAINING : 用户返回(从训练阶段)
```

### 状态特征描述

| 状态 | 主要功能 | 数据处理 | 用户交互 | 超时机制 |
|------|----------|----------|----------|----------|
| WAITING | 用户检测和认证 | 持续处理姿态数据 | 被动等待 | 无 |
| INTRODUCTION | 任务介绍和准备 | 监控用户状态 | 观看介绍 | 30秒 |
| PREPARATION | 动作引导视频 | 监控用户状态 | 观看视频 | 用户控制 |
| TRAINING | 动作训练和评分 | 实时姿态分析 | 主动训练 | 无 |
| REPORTING | 报告展示 | 生成训练报告 | 查看报告 | 60秒 |
| PAUSE | 系统暂停 | 保持状态上下文 | 等待返回 | 300秒 |

## 状态转换事件

### 事件枚举

```python
class StateTransitionEvent(str, Enum):
    """状态转换事件枚举"""
    # 系统级事件
    SYSTEM_INIT = "system_init"                    # 系统初始化

    # 用户相关事件
    LOGIN_SUCCESS = "login_success"                # 用户登录成功
    USER_LOST = "user_lost"                        # 用户丢失
    USER_NOT_AUTH = "user_not_auth"                # 用户未认证
    USER_BACK = "user_back"                        # 用户返回

    # 时钟事件
    CLOCK_INTRODUCTION_FINISH = "clock_introduction_finish"  # 介绍时钟结束
    CLOCK_REPORTING_FINISH = "clock_reporting_finish"        # 报告时钟结束

    # 训练相关事件
    ACTION_SWITCH = "action_switch"                # 动作切换
    ACTION_COMPLETED = "action_completed"          # 动作完成

    # 前端到后端事件
    PREPARING_FINISH = "preparing_finish"          # 准备阶段完成
```

### 事件触发条件

```python
class EventTriggerConditions:
    """事件触发条件定义"""

    @staticmethod
    def should_trigger_user_lost(detection_history: List[bool],
                                threshold_seconds: float = 3.0) -> bool:
        """判断是否应该触发用户丢失事件"""
        if len(detection_history) < threshold_seconds * 30:  # 假设30FPS
            return False

        # 检查最近3秒内是否持续检测不到用户
        recent_detections = detection_history[-int(threshold_seconds * 30):]
        return not any(recent_detections)

    @staticmethod
    def should_trigger_login_success(user_detection_count: int,
                                   confidence_threshold: float = 0.8) -> bool:
        """判断是否应该触发登录成功事件"""
        return user_detection_count >= 3  # 连续3次检测到同一用户
```

## 状态处理器架构

### 基础处理器接口

```python
from abc import ABC, abstractmethod
from typing import Dict, Any

class BaseStateHandler(ABC):
    """状态处理器基类"""

    def __init__(self, state: SystemState):
        """初始化状态处理器"""
        self.state = state
        self.logger = logging.getLogger(f"{__name__}.{state.value}")
        self.entry_time = None
        self.context_data = {}

    @abstractmethod
    def enter_state(self, context: Dict[str, Any]) -> None:
        """进入状态时的处理逻辑"""
        self.entry_time = time.time()
        self.logger.info(f"进入状态: {self.state.value}")

    @abstractmethod
    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理状态下的数据"""
        pass

    @abstractmethod
    def exit_state(self, context: Dict[str, Any]) -> None:
        """退出状态时的清理逻辑"""
        duration = time.time() - self.entry_time if self.entry_time else 0
        self.logger.info(f"退出状态: {self.state.value}, 持续时间: {duration:.2f}秒")

    def get_state_duration(self) -> float:
        """获取状态持续时间"""
        if self.entry_time:
            return time.time() - self.entry_time
        return 0.0

    def validate_context(self, context: Dict[str, Any]) -> bool:
        """验证上下文数据的有效性"""
        required_fields = ['current_time', 'session_active']
        return all(field in context for field in required_fields)
```

### 具体状态处理器实现

#### WAITING状态处理器

```python
class WaitingHandler(BaseStateHandler):
    """WAITING状态处理器 - 用户检测和认证"""

    def __init__(self):
        super().__init__(SystemState.WAITING)
        self.detection_threshold = 3  # 连续检测次数阈值
        self.detection_count = 0
        self.last_patient_id = None

    def enter_state(self, context: Dict[str, Any]):
        """进入WAITING状态"""
        super().enter_state(context)
        self.detection_count = 0
        self.last_patient_id = None
        self.logger.info("系统进入等待用户识别和登录状态")

    def handle_data(self, data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理WAITING状态下的数据"""
        try:
            # 处理用户检测逻辑
            detection_result = self._handle_user_detection(data, context)

            # 创建等待消息数据
            waiting_message_data = self._create_waiting_message(data, context, detection_result)

            # 如果检测到用户并认证成功，触发状态转换
            if detection_result.get("user_authenticated"):
                return {
                    "success": True,
                    "trigger_event": StateTransitionEvent.LOGIN_SUCCESS,
                    "next_state": SystemState.INTRODUCTION,
                    "websocket_message": MessageType.LOGIN_SUCCESS,
                    "state_data": detection_result["state_data"],
                }

            # 未检测到用户或认证失败，只发送waiting_message
            return {
                "success": True,
                "websocket_message": MessageType.WAITING_MESSAGE,
                "state_data": waiting_message_data,
            }

        except Exception as e:
            self.logger.error(f"处理等待状态数据失败: {e}")
            return {"success": False, "error": str(e)}

    def _handle_user_detection(self, pose_data: ZMQDetectData,
                              context: Dict[str, Any]) -> Dict[str, Any]:
        """处理用户检测和认证逻辑"""
        # 使用用户检测服务进行检测和认证
        from ..user_detection_service import user_detection_service
        return user_detection_service.detect_and_authenticate_user(pose_data, context)
```

#### TRAINING状态处理器

```python
class TrainingHandler(BaseStateHandler):
    """TRAINING状态处理器 - 动作训练和评分"""

    def __init__(self):
        super().__init__(SystemState.TRAINING)
        self.action_evaluator = None  # 动作评估器
        self.score_history = []
        self.current_rep_start_time = None

    def enter_state(self, context: Dict[str, Any]):
        """进入TRAINING状态"""
        super().enter_state(context)
        self.score_history = []
        self.current_rep_start_time = time.time()
        self.logger.info("系统进入动作训练状态")

    def handle_data(self, data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理TRAINING状态下的数据"""
        try:
            # 检查用户状态
            user_status = self._check_user_status(data, context)

            # 检查是否需要暂停
            if user_status["should_pause"]:
                return self._handle_pause(user_status, context)

            # 处理动作识别和评分
            training_result = self._process_pose_data(data, context)

            # 检查是否完成动作或需要切换
            if training_result.get("action_completed"):
                return self._handle_action_completion(data, context, training_result)
            elif training_result.get("action_switch"):
                return self._handle_action_switch(data, context, training_result)

            # 发送训练消息
            return self._send_training_message(data, context, training_result)

        except Exception as e:
            self.logger.error(f"处理训练状态数据失败: {e}")
            return {"success": False, "error": str(e)}

    def _process_pose_data(self, data: ZMQDetectData,
                          context: Dict[str, Any]) -> Dict[str, Any]:
        """处理姿态数据，进行动作识别和评分"""
        current_action = context.get("current_action")
        if not current_action:
            return {"success": False, "error": "当前动作信息缺失"}

        # 使用动作评估器进行评分
        if not self.action_evaluator:
            from ..action_evaluation_service import action_evaluation_service
            self.action_evaluator = action_evaluation_service

        # 评估当前动作
        evaluation_result = self.action_evaluator.evaluate_action(
            pose_keypoints=data.pose_keypoints,
            action_info=current_action,
            context=context
        )

        # 更新分数历史
        if evaluation_result.get("score") is not None:
            self.score_history.append(evaluation_result["score"])

        return evaluation_result
```

### 状态处理器工厂

```python
class StateHandlerFactory:
    """状态处理器工厂"""

    def __init__(self):
        """初始化状态处理器工厂"""
        self.logger = logging.getLogger(__name__)
        self._handlers = {}
        self._initialize_handlers()

    def _initialize_handlers(self):
        """初始化所有状态处理器"""
        try:
            # 延迟导入避免循环依赖
            from .waiting_handler import WaitingHandler
            from .introduction_handler import IntroductionHandler
            from .preparation_handler import PreparationHandler
            from .training_handler import TrainingHandler
            from .reporting_handler import ReportingHandler
            from .pause_handler import PauseHandler

            # 注册所有状态处理器
            self._handlers = {
                SystemState.WAITING: WaitingHandler(),
                SystemState.INTRODUCTION: IntroductionHandler(),
                SystemState.PREPARATION: PreparationHandler(),
                SystemState.TRAINING: TrainingHandler(),
                SystemState.REPORTING: ReportingHandler(),
                SystemState.PAUSE: PauseHandler()
            }

            self.logger.info(f"状态处理器工厂初始化完成: {len(self._handlers)} 个处理器")

        except Exception as e:
            self.logger.error(f"状态处理器工厂初始化失败: {e}")
            raise

    def get_handler(self, state: SystemState) -> Optional[BaseStateHandler]:
        """获取指定状态的处理器"""
        handler = self._handlers.get(state)
        if not handler:
            self.logger.warning(f"未找到状态处理器: {state.value}")
        return handler

    def get_all_handlers(self) -> Dict[SystemState, BaseStateHandler]:
        """获取所有状态处理器"""
        return self._handlers.copy()

    def register_handler(self, state: SystemState, handler: BaseStateHandler):
        """注册新的状态处理器"""
        self._handlers[state] = handler
        self.logger.info(f"注册状态处理器: {state.value}")

    def get_handler_stats(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        stats = {}
        for state, handler in self._handlers.items():
            stats[state.value] = {
                "handler_class": handler.__class__.__name__,
                "state_duration": handler.get_state_duration(),
                "entry_time": handler.entry_time
            }
        return stats

# 全局状态处理器工厂实例
state_handler_factory = StateHandlerFactory()
```

## 状态管理器

### 状态上下文

```python
@dataclass
class StateContext:
    """状态上下文数据结构"""
    current_state: SystemState                    # 当前状态
    previous_state: Optional[SystemState] = None # 前一状态
    state_start_time: float = 0.0               # 状态开始时间
    state_data: Dict[str, Any] = field(default_factory=dict)  # 状态数据
    transition_history: List[str] = field(default_factory=list)  # 转换历史
```

### 状态管理器实现

```python
class StateManager:
    """状态机管理器"""

    def __init__(self):
        """初始化状态管理器"""
        self.logger = logging.getLogger(__name__)

        # 当前状态上下文
        self.current_context = StateContext(
            current_state=SystemState.WAITING,
            state_start_time=time.time()
        )

        # 状态变化回调函数
        self.state_change_callbacks: Dict[SystemState, list] = {}

        # 状态转换锁
        self.transition_lock = threading.Lock()

        # 统计信息
        self.stats = {
            'total_transitions': 0,
            'successful_transitions': 0,
            'failed_transitions': 0,
            'current_state_duration': 0.0
        }

        self.logger.info("状态机管理器初始化完成")

    def get_current_state(self) -> SystemState:
        """获取当前状态"""
        return self.current_context.current_state

    def get_current_context(self) -> StateContext:
        """获取当前状态上下文"""
        return self.current_context

    def transition_to(self, event: StateTransitionEvent, **kwargs) -> bool:
        """执行状态转换

        Args:
            event: 转换事件
            **kwargs: 转换时传递的额外数据

        Returns:
            bool: 转换是否成功
        """
        with self.transition_lock:
            try:
                self.stats['total_transitions'] += 1

                # 验证转换事件
                if not StateValidator.validate_transition_event(event, self.current_context):
                    self.logger.warning(f"无效的状态转换事件: {event.value}")
                    self.stats['failed_transitions'] += 1
                    return False

                # 获取转换规则
                transition = StateTransitionRules.get_transition(
                    self.current_context.current_state,
                    event
                )

                if not transition:
                    self.logger.warning(
                        f"未找到转换规则: {self.current_context.current_state.value} -> {event.value}"
                    )
                    self.stats['failed_transitions'] += 1
                    return False

                # 执行状态转换
                old_state = self.current_context.current_state
                new_state = transition.to_state

                # 更新状态上下文
                self.current_context.previous_state = old_state
                self.current_context.current_state = new_state
                self.current_context.state_start_time = time.time()

                # 更新状态数据
                if kwargs:
                    self.current_context.state_data = self.current_context.state_data or {}
                    self.current_context.state_data.update(kwargs)

                # 记录转换历史
                transition_record = f"{old_state.value} -> {new_state.value} ({event.value})"
                self.current_context.transition_history.append(transition_record)

                self.stats['successful_transitions'] += 1

                self.logger.info(
                    f"状态转换成功: {old_state.value} -> {new_state.value} (事件: {event.value})"
                )

                # 触发状态变化回调
                self._trigger_state_callbacks(new_state, old_state)

                return True

            except Exception as e:
                self.logger.error(f"状态转换失败: {e}")
                self.stats['failed_transitions'] += 1
                return False

    def register_state_callback(self, state: SystemState, callback: callable):
        """注册状态变化回调函数"""
        if state not in self.state_change_callbacks:
            self.state_change_callbacks[state] = []
        self.state_change_callbacks[state].append(callback)

    def _trigger_state_callbacks(self, new_state: SystemState, old_state: SystemState):
        """触发状态变化回调"""
        callbacks = self.state_change_callbacks.get(new_state, [])
        for callback in callbacks:
            try:
                callback(new_state, old_state, self.current_context)
            except Exception as e:
                self.logger.error(f"状态回调执行失败: {e}")

    def reset_to_waiting(self):
        """重置状态机到WAITING状态"""
        try:
            with self.transition_lock:
                old_state = self.current_context.current_state

                # 重置到WAITING状态
                self.current_context.previous_state = old_state
                self.current_context.current_state = SystemState.WAITING
                self.current_context.state_start_time = time.time()
                self.current_context.state_data = {}

                # 触发回调
                self._trigger_state_callbacks(SystemState.WAITING, old_state)

                self.logger.info(f"状态重置完成: {old_state.value} -> WAITING")

        except Exception as e:
            self.logger.error(f"状态重置到WAITING失败: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """获取状态管理器统计信息"""
        current_duration = time.time() - self.current_context.state_start_time
        return {
            **self.stats,
            'current_state': self.current_context.current_state.value,
            'current_state_duration': current_duration,
            'transition_history': self.current_context.transition_history[-10:]  # 最近10次转换
        }

# 全局状态管理器实例
state_manager = StateManager()
```

## 状态转换规则

### 转换规则定义

```python
@dataclass
class StateTransition:
    """状态转换定义"""
    from_state: SystemState                      # 源状态
    to_state: SystemState                        # 目标状态
    event: StateTransitionEvent                  # 触发事件
    condition: Optional[str] = None              # 转换条件描述
    timeout: Optional[float] = None              # 超时时间（秒）
    priority: int = 0                           # 转换优先级

    def __str__(self) -> str:
        return f"{self.from_state.value} --[{self.event.value}]--> {self.to_state.value}"
```

### 时间常量定义

```python
class TimeConstants:
    """时间常量定义"""
    INTRODUCTION_DURATION = 30.0                 # 介绍阶段持续时间（秒）
    REPORTING_TIMEOUT = 60.0                     # 报告展示超时时间（秒）
    USER_LOST_TIMEOUT = 3.0                      # 用户丢失超时时间（秒）
    PAUSE_TIMEOUT = 300.0                        # 暂停状态超时时间（秒）
    ACTION_PREPARATION_TIMEOUT = 120.0           # 动作准备超时时间（秒）
```

### 状态转换规则集

```python
class StateTransitionRules:
    """状态转换规则管理"""

    # 定义所有有效的状态转换
    VALID_TRANSITIONS: List[StateTransition] = [
        # 1. waiting -> introduction (login_success)
        StateTransition(
            from_state=SystemState.WAITING,
            to_state=SystemState.INTRODUCTION,
            event=StateTransitionEvent.LOGIN_SUCCESS,
            condition="用户认证成功，进入欢迎介绍阶段",
            priority=1
        ),

        # 2. introduction -> preparation (clock_introduction_finish)
        StateTransition(
            from_state=SystemState.INTRODUCTION,
            to_state=SystemState.PREPARATION,
            event=StateTransitionEvent.CLOCK_INTRODUCTION_FINISH,
            condition="介绍阶段30秒时钟结束",
            timeout=TimeConstants.INTRODUCTION_DURATION,
            priority=1
        ),

        # 3. preparation -> training (preparing_finish)
        StateTransition(
            from_state=SystemState.PREPARATION,
            to_state=SystemState.TRAINING,
            event=StateTransitionEvent.PREPARING_FINISH,
            condition="前端确认准备完成，开始训练",
            priority=1
        ),

        # 4. training -> preparation (action_switch)
        StateTransition(
            from_state=SystemState.TRAINING,
            to_state=SystemState.PREPARATION,
            event=StateTransitionEvent.ACTION_SWITCH,
            condition="当前动作完成，切换到下一个动作",
            priority=1
        ),

        # 5. training -> reporting (action_completed)
        StateTransition(
            from_state=SystemState.TRAINING,
            to_state=SystemState.REPORTING,
            event=StateTransitionEvent.ACTION_COMPLETED,
            condition="所有动作训练完成，生成报告",
            priority=1
        ),

        # 6. reporting -> waiting (clock_reporting_finish)
        StateTransition(
            from_state=SystemState.REPORTING,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.CLOCK_REPORTING_FINISH,
            condition="报告展示完成或超时，返回等待状态",
            timeout=TimeConstants.REPORTING_TIMEOUT,
            priority=1
        ),

        # 7. 暂停相关转换 - introduction/preparation/training -> pause
        StateTransition(
            from_state=SystemState.INTRODUCTION,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_LOST,
            condition="用户消失超过3秒",
            timeout=TimeConstants.USER_LOST_TIMEOUT,
            priority=2
        ),
        StateTransition(
            from_state=SystemState.PREPARATION,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_LOST,
            condition="用户消失超过3秒",
            timeout=TimeConstants.USER_LOST_TIMEOUT,
            priority=2
        ),
        StateTransition(
            from_state=SystemState.TRAINING,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_LOST,
            condition="用户消失超过3秒",
            timeout=TimeConstants.USER_LOST_TIMEOUT,
            priority=2
        ),

        # 8. 用户未认证转换
        StateTransition(
            from_state=SystemState.INTRODUCTION,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_NOT_AUTH,
            condition="检测到非登录用户",
            priority=3
        ),
        StateTransition(
            from_state=SystemState.PREPARATION,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_NOT_AUTH,
            condition="检测到非登录用户",
            priority=3
        ),
        StateTransition(
            from_state=SystemState.TRAINING,
            to_state=SystemState.PAUSE,
            event=StateTransitionEvent.USER_NOT_AUTH,
            condition="检测到非登录用户",
            priority=3
        ),

        # 9. 用户返回转换 - pause -> 原状态
        StateTransition(
            from_state=SystemState.PAUSE,
            to_state=SystemState.INTRODUCTION,
            event=StateTransitionEvent.USER_BACK,
            condition="用户返回，恢复到介绍阶段",
            priority=1
        ),
        StateTransition(
            from_state=SystemState.PAUSE,
            to_state=SystemState.PREPARATION,
            event=StateTransitionEvent.USER_BACK,
            condition="用户返回，恢复到准备阶段",
            priority=1
        ),
        StateTransition(
            from_state=SystemState.PAUSE,
            to_state=SystemState.TRAINING,
            event=StateTransitionEvent.USER_BACK,
            condition="用户返回，恢复到训练阶段",
            priority=1
        ),

        # 10. 暂停超时转换
        StateTransition(
            from_state=SystemState.PAUSE,
            to_state=SystemState.WAITING,
            event=StateTransitionEvent.CLOCK_REPORTING_FINISH,  # 复用报告结束事件
            condition="暂停超时，返回等待状态",
            timeout=TimeConstants.PAUSE_TIMEOUT,
            priority=4
        )
    ]

    @classmethod
    def get_transition(cls, from_state: SystemState,
                      event: StateTransitionEvent) -> Optional[StateTransition]:
        """根据当前状态和事件获取转换规则"""
        # 按优先级排序，优先级高的先匹配
        sorted_transitions = sorted(
            [t for t in cls.VALID_TRANSITIONS
             if t.from_state == from_state and t.event == event],
            key=lambda x: x.priority
        )

        return sorted_transitions[0] if sorted_transitions else None

    @classmethod
    def get_valid_transitions_from_state(cls, state: SystemState) -> List[StateTransition]:
        """获取指定状态的所有有效转换"""
        return [t for t in cls.VALID_TRANSITIONS if t.from_state == state]

    @classmethod
    def is_valid_transition(cls, from_state: SystemState, to_state: SystemState,
                          event: StateTransitionEvent) -> bool:
        """检查状态转换是否有效"""
        for transition in cls.VALID_TRANSITIONS:
            if (transition.from_state == from_state and
                transition.to_state == to_state and
                transition.event == event):
                return True
        return False

    @classmethod
    def get_transition_graph(cls) -> Dict[str, List[str]]:
        """获取状态转换图的邻接表表示"""
        graph = {}
        for transition in cls.VALID_TRANSITIONS:
            from_state = transition.from_state.value
            to_state = transition.to_state.value

            if from_state not in graph:
                graph[from_state] = []

            if to_state not in graph[from_state]:
                graph[from_state].append(to_state)

        return graph
```

## 状态验证机制

### 状态验证器

```python
class StateValidator:
    """状态验证器"""

    @staticmethod
    def validate_state_context(context: StateContext) -> bool:
        """验证状态上下文的有效性"""
        if not isinstance(context.current_state, SystemState):
            return False

        if context.previous_state and not isinstance(context.previous_state, SystemState):
            return False

        if context.state_start_time <= 0:
            return False

        return True

    @staticmethod
    def validate_transition_event(event: StateTransitionEvent,
                                context: StateContext) -> bool:
        """验证转换事件在当前上下文中是否有效"""
        # 检查是否有对应的转换规则
        transition = StateTransitionRules.get_transition(context.current_state, event)
        if not transition:
            return False

        # 检查超时条件
        if transition.timeout:
            current_duration = time.time() - context.state_start_time
            if current_duration < transition.timeout:
                # 对于超时事件，需要达到超时时间才能触发
                if event in [StateTransitionEvent.CLOCK_INTRODUCTION_FINISH,
                           StateTransitionEvent.CLOCK_REPORTING_FINISH]:
                    return False

        return True

    @staticmethod
    def validate_handler_result(result: Dict[str, Any]) -> bool:
        """验证处理器返回结果的有效性"""
        if not isinstance(result, dict):
            return False

        # 检查必需字段
        if 'success' not in result:
            return False

        # 如果成功，检查其他字段
        if result['success']:
            # 如果有状态转换，检查相关字段
            if 'trigger_event' in result:
                required_fields = ['next_state', 'websocket_message']
                if not all(field in result for field in required_fields):
                    return False

        return True

    @staticmethod
    def validate_system_integrity() -> Dict[str, Any]:
        """验证整个状态系统的完整性"""
        issues = []

        # 检查所有状态是否都有对应的处理器
        from .state_handlers import state_handler_factory
        all_states = list(SystemState)
        registered_handlers = state_handler_factory.get_all_handlers()

        for state in all_states:
            if state not in registered_handlers:
                issues.append(f"状态 {state.value} 缺少对应的处理器")

        # 检查状态转换图的连通性
        graph = StateTransitionRules.get_transition_graph()
        unreachable_states = []

        for state in all_states:
            if state.value not in graph and state != SystemState.WAITING:
                unreachable_states.append(state.value)

        if unreachable_states:
            issues.append(f"不可达状态: {unreachable_states}")

        # 检查是否存在死锁状态
        dead_end_states = []
        for state_name, transitions in graph.items():
            if not transitions and state_name != SystemState.WAITING.value:
                dead_end_states.append(state_name)

        if dead_end_states:
            issues.append(f"死锁状态: {dead_end_states}")

        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "total_states": len(all_states),
            "registered_handlers": len(registered_handlers),
            "total_transitions": len(StateTransitionRules.VALID_TRANSITIONS)
        }
```

## 最佳实践

### 1. 状态处理器设计原则

**单一职责原则**:
- 每个状态处理器只负责一个状态的业务逻辑
- 避免在处理器中处理其他状态的逻辑
- 保持处理器的简洁和专注

**状态无关性**:
- 处理器不应该直接依赖其他状态的内部实现
- 通过上下文传递必要的状态信息
- 避免状态处理器之间的直接耦合

**错误处理**:
```python
def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
    """处理数据的标准模式"""
    try:
        # 验证输入数据
        if not self._validate_input(data, context):
            return {"success": False, "error": "输入数据无效"}

        # 执行业务逻辑
        result = self._process_business_logic(data, context)

        # 验证输出结果
        if not self._validate_output(result):
            return {"success": False, "error": "输出结果无效"}

        return result

    except Exception as e:
        self.logger.error(f"处理数据失败: {e}")
        return {"success": False, "error": str(e)}
```

### 2. 状态转换最佳实践

**事件驱动转换**:
```python
# 好的做法：基于明确的事件触发转换
if user_authenticated:
    state_manager.transition_to(StateTransitionEvent.LOGIN_SUCCESS, user_info=user_info)

# 避免的做法：直接修改状态
# state_manager.current_context.current_state = SystemState.INTRODUCTION  # 错误！
```

**条件检查**:
```python
def should_transition_to_pause(self, detection_history: List[bool]) -> bool:
    """检查是否应该转换到暂停状态"""
    # 明确的条件逻辑
    if len(detection_history) < 90:  # 3秒 * 30FPS
        return False

    recent_detections = detection_history[-90:]
    return not any(recent_detections)
```

### 3. 性能优化

**状态缓存**:
```python
class OptimizedStateHandler(BaseStateHandler):
    def __init__(self, state: SystemState):
        super().__init__(state)
        self._result_cache = {}
        self._cache_ttl = 0.1  # 100ms缓存

    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        # 使用缓存避免重复计算
        cache_key = self._generate_cache_key(data, context)

        if cache_key in self._result_cache:
            cached_result, timestamp = self._result_cache[cache_key]
            if time.time() - timestamp < self._cache_ttl:
                return cached_result

        # 执行实际处理
        result = self._do_handle_data(data, context)

        # 缓存结果
        self._result_cache[cache_key] = (result, time.time())

        return result
```

### 4. 调试和监控

**状态转换日志**:
```python
class StateTransitionLogger:
    def __init__(self):
        self.transition_log = []

    def log_transition(self, from_state: SystemState, to_state: SystemState,
                      event: StateTransitionEvent, context: Dict[str, Any]):
        """记录状态转换"""
        log_entry = {
            "timestamp": time.time(),
            "from_state": from_state.value,
            "to_state": to_state.value,
            "event": event.value,
            "context_summary": self._summarize_context(context)
        }
        self.transition_log.append(log_entry)

        # 保持日志大小
        if len(self.transition_log) > 1000:
            self.transition_log = self.transition_log[-500:]
```

**性能监控**:
```python
class StatePerformanceMonitor:
    def __init__(self):
        self.state_durations = {}
        self.handler_performance = {}

    def record_state_duration(self, state: SystemState, duration: float):
        """记录状态持续时间"""
        if state not in self.state_durations:
            self.state_durations[state] = []

        self.state_durations[state].append(duration)

        # 保持最近100次记录
        if len(self.state_durations[state]) > 100:
            self.state_durations[state] = self.state_durations[state][-50:]

    def get_average_duration(self, state: SystemState) -> float:
        """获取状态平均持续时间"""
        durations = self.state_durations.get(state, [])
        return sum(durations) / len(durations) if durations else 0.0
```

## 故障排除

### 常见问题

1. **状态转换失败**
   - 检查转换规则是否正确定义
   - 验证事件触发条件是否满足
   - 确认状态验证器的逻辑

2. **处理器性能问题**
   - 使用性能监控工具分析瓶颈
   - 优化数据处理逻辑
   - 考虑使用缓存机制

3. **状态不一致**
   - 检查并发访问的线程安全性
   - 验证状态上下文的完整性
   - 使用状态验证器进行完整性检查

### 调试工具

```python
def debug_state_system():
    """调试状态系统"""
    # 验证系统完整性
    integrity_result = StateValidator.validate_system_integrity()
    print(f"系统完整性检查: {integrity_result}")

    # 显示当前状态信息
    current_context = state_manager.get_current_context()
    print(f"当前状态: {current_context.current_state.value}")
    print(f"状态持续时间: {time.time() - current_context.state_start_time:.2f}秒")

    # 显示可用转换
    valid_transitions = StateTransitionRules.get_valid_transitions_from_state(
        current_context.current_state
    )
    print(f"可用转换: {[str(t) for t in valid_transitions]}")

    # 显示统计信息
    stats = state_manager.get_stats()
    print(f"状态管理器统计: {stats}")
```

---

*本文档基于智能康复系统v3.0实现，最后更新时间：2023-12-21*