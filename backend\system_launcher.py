"""
智能康复系统 - 系统启动器
统一管理系统启动流程，确保组件正确初始化
"""
import os
import sys
import logging
import signal
from typing import Optional
from flask import Flask

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from app import create_app
from extensions import socketio
from utils.logger_setup import setup_system_logging

class SystemLauncher:
    """系统启动器"""
    
    def __init__(self):
        """初始化系统启动器"""
        self.app: Optional[Flask] = None
        self._setup_signal_handlers()
        self.logger = logging.getLogger(__name__)  # 先初始化一个基本的logger
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            print(f"\n💥 收到退出信号 {signum}，正在关闭系统...")
            self._cleanup()
            os._exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def initialize(self):
        """初始化系统"""
        try:
            # 设置日志
            setup_system_logging()
            self.logger = logging.getLogger(__name__)
            self.logger.info("开始初始化智能康复系统")
            
            # 创建Flask应用
            self.app = create_app()
            # 验证关键组件
            self._verify_components()
            
            self.logger.info("系统初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"系统初始化失败: {e}")
            else:
                print(f"❌ 系统初始化失败: {e}")
            return False
    
    def _verify_components(self):
        """验证关键组件"""
        try:
            # 验证业务组件
            from services.business.system_coordinator import system_coordinator
            from services.business.state_manager import state_manager
            from services.communication.zmq.zmq_receiver import zmq_receiver
            from services.communication.websocket.websocket_manager import websocket_manager
            self.logger.info("✓ 所有关键组件验证通过")
            
        except ImportError as e:
            raise Exception(f"关键组件导入失败: {e}")
    
    def run(self):
        """运行系统"""
        if not self.app:
            if not self.initialize():
                return False
        try:
            self.logger.info("🚀 启动智能康复系统服务器")
            self.logger.info("📡 WebSocket服务: ws://localhost:5000")
            self.logger.info("🎥 视频流服务: http://localhost:5000/video_feed")
            self.logger.info("💡 按 Ctrl+C 停止服务")
            
            # 使用SocketIO运行应用
            socketio.run(
                self.app,
                host='0.0.0.0',
                port=5000,
                debug=False,
                use_reloader=False,
                log_output=True,
            )
            
        except Exception as e:
            self.logger.error(f"系统运行失败: {e}")
            return False
        
        return True
    
    def _cleanup(self):
        """清理资源"""
        if self.logger:
            self.logger.info("正在清理系统资源...")
        try:
            # 清理ZMQ资源
            from services.communication.zmq.zmq_receiver import zmq_receiver
            zmq_receiver.cleanup()
            
            if self.logger:
                self.logger.info("系统资源清理完成")
        except Exception as e:
            if self.logger:
                self.logger.error(f"资源清理失败: {e}")

def create_launcher():
    """创建系统启动器实例"""
    return SystemLauncher()

if __name__ == '__main__':
    launcher = create_launcher()
    success = launcher.run()
    sys.exit(0 if success else 1)