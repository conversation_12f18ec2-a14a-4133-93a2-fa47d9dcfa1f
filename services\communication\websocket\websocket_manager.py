"""
智能康复系统 - WebSocket管理器
处理前端WebSocket连接和消息发送
"""
import logging
import json
from typing import Dict, Any, Optional
from flask import Flask
from flask_socketio import SocketIO

class WebSocketManager:
    """WebSocket管理器类"""
    
    def __init__(self):
        """初始化WebSocket管理器"""
        self.logger = logging.getLogger(__name__)
        self.app = None
        self.socketio = None
        self.system_coordinator = None
        self.zmq_receiver = None
        self.is_connected = False
        
    def initialize(self, app: Flask, socketio: SocketIO):
        """初始化WebSocket管理器"""
        self.app = app
        self.socketio = socketio
        self._register_handlers()
        self.logger.info("WebSocket管理器初始化完成")
    
    def _register_handlers(self):
        """注册SocketIO事件处理器"""
        if not self.socketio:
            self.logger.error("SocketIO实例未初始化，无法注册处理器")
            return
            
        @self.socketio.on('connect')
        def handle_connect():
            """处理客户端连接"""
            self.is_connected = True
            self.logger.info("前端客户端已连接")
            # 通知ZMQ接收器前端已连接
            if self.zmq_receiver:
                self.zmq_receiver.set_frontend_connected(True)
            # 通知系统协调器前端已连接
            if self.system_coordinator:
                self.system_coordinator.set_frontend_connected(True)
            # 发送初始状态
            self.send_system_status()
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """处理客户端断开连接"""
            self.is_connected = False
            self.logger.info("前端客户端已断开连接")
            # 通知ZMQ接收器前端已断开
            if self.zmq_receiver:
                self.zmq_receiver.set_frontend_connected(False)
            # 通知系统协调器前端已断开
            if self.system_coordinator:
                self.system_coordinator.set_frontend_connected(False)
        
        @self.socketio.on('client_command')
        def handle_client_command(data):
            """处理客户端命令"""
            self.logger.info(f"收到客户端命令: {data}")
            if self.system_coordinator:
                self.system_coordinator.handle_client_command(data)
                return {'status': 'success', 'message': '命令已处理'}
            return {'status': 'error', 'message': '系统协调器未初始化'}
    
    def set_system_coordinator(self, coordinator):
        """设置系统协调器引用"""
        self.system_coordinator = coordinator
        self.logger.info("系统协调器引用已设置")
    
    def send_system_status(self):
        """发送系统状态"""
        if not self.system_coordinator:
            self.logger.warning("系统协调器未初始化，无法获取状态")
            return
        
        try:
            status = self.system_coordinator.get_system_status()
            self.send_message('system_status', status)
        except Exception as e:
            self.logger.error(f"发送系统状态失败: {e}")
    
    def send_state_update(self, state_data):
        """发送状态更新"""
        self.send_message('state_update', state_data)
    
    def send_pose_data(self, pose_data):
        """发送姿态数据"""
        self.send_message('pose_data', pose_data)
    
    def send_message(self, message_type: str, message_data: Dict[str, Any]):
        """发送WebSocket消息"""
        if not self.socketio or not self.is_connected:
            self.logger.debug(f"无法发送消息 {message_type}: SocketIO未初始化或前端未连接")
            return
        
        try:
            # 确保在Flask应用上下文中发送消息
            with self.app.app_context():
                self.socketio.emit(message_type, message_data, namespace='/')
                self.logger.debug(f"已发送消息: {message_type}")
        except Exception as e:
            self.logger.error(f"发送消息失败 {message_type}: {e}")

# 创建单例实例
websocket_manager = WebSocketManager()