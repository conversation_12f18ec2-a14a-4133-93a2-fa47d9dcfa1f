/**
 * 骨架连接关系定义
 * 基于Python const.py中的keypoint_info和skeleton_info定义
 * 支持RTMPose 133个关键点的完整姿态渲染
 */

/**
 * 关键点信息定义 - 从Python const.py转换而来
 * 包含每个关键点的名称、ID、颜色、类型等信息
 */
export const KEYPOINT_INFO = {
  // 身体关键点 (0-22)
  0: { name: 'nose', id: 0, color: [51, 153, 255], type: 'upper' },
  1: { name: 'left_eye', id: 1, color: [51, 153, 255], type: 'upper' },
  2: { name: 'right_eye', id: 2, color: [51, 153, 255], type: 'upper' },
  3: { name: 'left_ear', id: 3, color: [51, 153, 255], type: 'upper' },
  4: { name: 'right_ear', id: 4, color: [51, 153, 255], type: 'upper' },
  5: { name: 'left_shoulder', id: 5, color: [0, 255, 0], type: 'upper' },
  6: { name: 'right_shoulder', id: 6, color: [255, 128, 0], type: 'upper' },
  7: { name: 'left_elbow', id: 7, color: [0, 255, 0], type: 'upper' },
  8: { name: 'right_elbow', id: 8, color: [255, 128, 0], type: 'upper' },
  9: { name: 'left_wrist', id: 9, color: [0, 255, 0], type: 'upper' },
  10: { name: 'right_wrist', id: 10, color: [255, 128, 0], type: 'upper' },
  11: { name: 'left_hip', id: 11, color: [0, 255, 0], type: 'lower' },
  12: { name: 'right_hip', id: 12, color: [255, 128, 0], type: 'lower' },
  13: { name: 'left_knee', id: 13, color: [0, 255, 0], type: 'lower' },
  14: { name: 'right_knee', id: 14, color: [255, 128, 0], type: 'lower' },
  15: { name: 'left_ankle', id: 15, color: [0, 255, 0], type: 'lower' },
  16: { name: 'right_ankle', id: 16, color: [255, 128, 0], type: 'lower' },
  17: { name: 'left_big_toe', id: 17, color: [255, 128, 0], type: 'lower' },
  18: { name: 'left_small_toe', id: 18, color: [255, 128, 0], type: 'lower' },
  19: { name: 'left_heel', id: 19, color: [255, 128, 0], type: 'lower' },
  20: { name: 'right_big_toe', id: 20, color: [255, 128, 0], type: 'lower' },
  21: { name: 'right_small_toe', id: 21, color: [255, 128, 0], type: 'lower' },
  22: { name: 'right_heel', id: 22, color: [255, 128, 0], type: 'lower' },

  // 面部关键点 (23-90) - 暂时跳过，因为要求不绘制面部

  // 手部关键点 (91-132)
  91: { name: 'left_hand_root', id: 91, color: [255, 255, 255], type: '' },
  92: { name: 'left_thumb1', id: 92, color: [255, 128, 0], type: '' },
  93: { name: 'left_thumb2', id: 93, color: [255, 128, 0], type: '' },
  94: { name: 'left_thumb3', id: 94, color: [255, 128, 0], type: '' },
  95: { name: 'left_thumb4', id: 95, color: [255, 128, 0], type: '' },
  96: { name: 'left_forefinger1', id: 96, color: [255, 153, 255], type: '' },
  97: { name: 'left_forefinger2', id: 97, color: [255, 153, 255], type: '' },
  98: { name: 'left_forefinger3', id: 98, color: [255, 153, 255], type: '' },
  99: { name: 'left_forefinger4', id: 99, color: [255, 153, 255], type: '' },
  100: { name: 'left_middle_finger1', id: 100, color: [102, 178, 255], type: '' },
  101: { name: 'left_middle_finger2', id: 101, color: [102, 178, 255], type: '' },
  102: { name: 'left_middle_finger3', id: 102, color: [102, 178, 255], type: '' },
  103: { name: 'left_middle_finger4', id: 103, color: [102, 178, 255], type: '' },
  104: { name: 'left_ring_finger1', id: 104, color: [255, 51, 51], type: '' },
  105: { name: 'left_ring_finger2', id: 105, color: [255, 51, 51], type: '' },
  106: { name: 'left_ring_finger3', id: 106, color: [255, 51, 51], type: '' },
  107: { name: 'left_ring_finger4', id: 107, color: [255, 51, 51], type: '' },
  108: { name: 'left_pinky_finger1', id: 108, color: [0, 255, 0], type: '' },
  109: { name: 'left_pinky_finger2', id: 109, color: [0, 255, 0], type: '' },
  110: { name: 'left_pinky_finger3', id: 110, color: [0, 255, 0], type: '' },
  111: { name: 'left_pinky_finger4', id: 111, color: [0, 255, 0], type: '' },
  112: { name: 'right_hand_root', id: 112, color: [255, 255, 255], type: '' },
  113: { name: 'right_thumb1', id: 113, color: [255, 128, 0], type: '' },
  114: { name: 'right_thumb2', id: 114, color: [255, 128, 0], type: '' },
  115: { name: 'right_thumb3', id: 115, color: [255, 128, 0], type: '' },
  116: { name: 'right_thumb4', id: 116, color: [255, 128, 0], type: '' },
  117: { name: 'right_forefinger1', id: 117, color: [255, 153, 255], type: '' },
  118: { name: 'right_forefinger2', id: 118, color: [255, 153, 255], type: '' },
  119: { name: 'right_forefinger3', id: 119, color: [255, 153, 255], type: '' },
  120: { name: 'right_forefinger4', id: 120, color: [255, 153, 255], type: '' },
  121: { name: 'right_middle_finger1', id: 121, color: [102, 178, 255], type: '' },
  122: { name: 'right_middle_finger2', id: 122, color: [102, 178, 255], type: '' },
  123: { name: 'right_middle_finger3', id: 123, color: [102, 178, 255], type: '' },
  124: { name: 'right_middle_finger4', id: 124, color: [102, 178, 255], type: '' },
  125: { name: 'right_ring_finger1', id: 125, color: [255, 51, 51], type: '' },
  126: { name: 'right_ring_finger2', id: 126, color: [255, 51, 51], type: '' },
  127: { name: 'right_ring_finger3', id: 127, color: [255, 51, 51], type: '' },
  128: { name: 'right_ring_finger4', id: 128, color: [255, 51, 51], type: '' },
  129: { name: 'right_pinky_finger1', id: 129, color: [0, 255, 0], type: '' },
  130: { name: 'right_pinky_finger2', id: 130, color: [0, 255, 0], type: '' },
  131: { name: 'right_pinky_finger3', id: 131, color: [0, 255, 0], type: '' },
  132: { name: 'right_pinky_finger4', id: 132, color: [0, 255, 0], type: '' }
}

/**
 * 名称到索引的映射 - 用于从Python的名称连接转换为索引连接
 */
export const NAME_TO_INDEX = {}
Object.keys(KEYPOINT_INFO).forEach(key => {
  const info = KEYPOINT_INFO[key]
  NAME_TO_INDEX[info.name] = parseInt(key)
})

/**
 * 骨架连接信息定义 - 从Python const.py的skeleton_info转换而来
 * 每个连接包含起始点、结束点和颜色信息
 */
export const SKELETON_CONNECTIONS = [
  // 身体主要连接
  { start: 15, end: 13, color: [0, 255, 0] },      // left_ankle -> left_knee
  { start: 13, end: 11, color: [0, 255, 0] },      // left_knee -> left_hip
  { start: 16, end: 14, color: [255, 128, 0] },    // right_ankle -> right_knee
  { start: 14, end: 12, color: [255, 128, 0] },    // right_knee -> right_hip
  { start: 11, end: 12, color: [51, 153, 255] },   // left_hip -> right_hip
  { start: 5, end: 11, color: [51, 153, 255] },    // left_shoulder -> left_hip
  { start: 6, end: 12, color: [51, 153, 255] },    // right_shoulder -> right_hip
  { start: 5, end: 6, color: [51, 153, 255] },     // left_shoulder -> right_shoulder
  { start: 5, end: 7, color: [0, 255, 0] },        // left_shoulder -> left_elbow
  { start: 6, end: 8, color: [255, 128, 0] },      // right_shoulder -> right_elbow
  { start: 7, end: 9, color: [0, 255, 0] },        // left_elbow -> left_wrist
  { start: 8, end: 10, color: [255, 128, 0] },     // right_elbow -> right_wrist

  // 头部连接（暂时保留，但可以选择不绘制）
  { start: 1, end: 2, color: [51, 153, 255] },     // left_eye -> right_eye
  { start: 0, end: 1, color: [51, 153, 255] },     // nose -> left_eye
  { start: 0, end: 2, color: [51, 153, 255] },     // nose -> right_eye
  { start: 1, end: 3, color: [51, 153, 255] },     // left_eye -> left_ear
  { start: 2, end: 4, color: [51, 153, 255] },     // right_eye -> right_ear
  { start: 3, end: 5, color: [51, 153, 255] },     // left_ear -> left_shoulder
  { start: 4, end: 6, color: [51, 153, 255] },     // right_ear -> right_shoulder

  // 脚部连接
  { start: 15, end: 17, color: [0, 255, 0] },      // left_ankle -> left_big_toe
  { start: 15, end: 18, color: [0, 255, 0] },      // left_ankle -> left_small_toe
  { start: 15, end: 19, color: [0, 255, 0] },      // left_ankle -> left_heel
  { start: 16, end: 20, color: [255, 128, 0] },    // right_ankle -> right_big_toe
  { start: 16, end: 21, color: [255, 128, 0] },    // right_ankle -> right_small_toe
  { start: 16, end: 22, color: [255, 128, 0] },    // right_ankle -> right_heel

  // 左手连接
  { start: 91, end: 92, color: [255, 128, 0] },    // left_hand_root -> left_thumb1
  { start: 92, end: 93, color: [255, 128, 0] },    // left_thumb1 -> left_thumb2
  { start: 93, end: 94, color: [255, 128, 0] },    // left_thumb2 -> left_thumb3
  { start: 94, end: 95, color: [255, 128, 0] },    // left_thumb3 -> left_thumb4
  { start: 91, end: 96, color: [255, 153, 255] },  // left_hand_root -> left_forefinger1
  { start: 96, end: 97, color: [255, 153, 255] },  // left_forefinger1 -> left_forefinger2
  { start: 97, end: 98, color: [255, 153, 255] },  // left_forefinger2 -> left_forefinger3
  { start: 98, end: 99, color: [255, 153, 255] },  // left_forefinger3 -> left_forefinger4
  { start: 91, end: 100, color: [102, 178, 255] }, // left_hand_root -> left_middle_finger1
  { start: 100, end: 101, color: [102, 178, 255] }, // left_middle_finger1 -> left_middle_finger2
  { start: 101, end: 102, color: [102, 178, 255] }, // left_middle_finger2 -> left_middle_finger3
  { start: 102, end: 103, color: [102, 178, 255] }, // left_middle_finger3 -> left_middle_finger4
  { start: 91, end: 104, color: [255, 51, 51] },   // left_hand_root -> left_ring_finger1
  { start: 104, end: 105, color: [255, 51, 51] },  // left_ring_finger1 -> left_ring_finger2
  { start: 105, end: 106, color: [255, 51, 51] },  // left_ring_finger2 -> left_ring_finger3
  { start: 106, end: 107, color: [255, 51, 51] },  // left_ring_finger3 -> left_ring_finger4
  { start: 91, end: 108, color: [0, 255, 0] },     // left_hand_root -> left_pinky_finger1
  { start: 108, end: 109, color: [0, 255, 0] },    // left_pinky_finger1 -> left_pinky_finger2
  { start: 109, end: 110, color: [0, 255, 0] },    // left_pinky_finger2 -> left_pinky_finger3
  { start: 110, end: 111, color: [0, 255, 0] },    // left_pinky_finger3 -> left_pinky_finger4

  // 右手连接
  { start: 112, end: 113, color: [255, 128, 0] },  // right_hand_root -> right_thumb1
  { start: 113, end: 114, color: [255, 128, 0] },  // right_thumb1 -> right_thumb2
  { start: 114, end: 115, color: [255, 128, 0] },  // right_thumb2 -> right_thumb3
  { start: 115, end: 116, color: [255, 128, 0] },  // right_thumb3 -> right_thumb4
  { start: 112, end: 117, color: [255, 153, 255] }, // right_hand_root -> right_forefinger1
  { start: 117, end: 118, color: [255, 153, 255] }, // right_forefinger1 -> right_forefinger2
  { start: 118, end: 119, color: [255, 153, 255] }, // right_forefinger2 -> right_forefinger3
  { start: 119, end: 120, color: [255, 153, 255] }, // right_forefinger3 -> right_forefinger4
  { start: 112, end: 121, color: [102, 178, 255] }, // right_hand_root -> right_middle_finger1
  { start: 121, end: 122, color: [102, 178, 255] }, // right_middle_finger1 -> right_middle_finger2
  { start: 122, end: 123, color: [102, 178, 255] }, // right_middle_finger2 -> right_middle_finger3
  { start: 123, end: 124, color: [102, 178, 255] }, // right_middle_finger3 -> right_middle_finger4
  { start: 112, end: 125, color: [255, 51, 51] },  // right_hand_root -> right_ring_finger1
  { start: 125, end: 126, color: [255, 51, 51] },  // right_ring_finger1 -> right_ring_finger2
  { start: 126, end: 127, color: [255, 51, 51] },  // right_ring_finger2 -> right_ring_finger3
  { start: 127, end: 128, color: [255, 51, 51] },  // right_ring_finger3 -> right_ring_finger4
  { start: 112, end: 129, color: [0, 255, 0] },    // right_hand_root -> right_pinky_finger1
  { start: 129, end: 130, color: [0, 255, 0] },    // right_pinky_finger1 -> right_pinky_finger2
  { start: 130, end: 131, color: [0, 255, 0] },    // right_pinky_finger2 -> right_pinky_finger3
  { start: 131, end: 132, color: [0, 255, 0] },    // right_pinky_finger3 -> right_pinky_finger4
]
/**
 * 身体主要骨架连接（简化版）- 只包含身体核心连接
 */
export const BODY_SKELETON_CONNECTIONS = SKELETON_CONNECTIONS.filter(conn => {
  // 只保留身体核心连接（0-22索引范围）
  return conn.start <= 22 && conn.end <= 22 &&
         // 排除头部连接（可选）
         !(conn.start <= 4 && conn.end <= 4)
})
/**
 * 完整骨架连接（包含手部）
 */
export const FULL_SKELETON_CONNECTIONS = SKELETON_CONNECTIONS

/**
 * 动作特定的关键点连接映射
 */
export const ACTION_SPECIFIC_CONNECTIONS = {
  // 肩部触摸动作
  shoulder_touch: [
    [5, 6],   // 肩部连接
    [5, 7],   // 左肩到左肘
    [7, 9],   // 左肘到左腕
    [6, 8],   // 右肩到右肘
    [8, 10],  // 右肘到右腕
    [0, 5],   // 头部到左肩
    [0, 6]    // 头部到右肩
  ],
  
  // 手臂举起动作
  arm_raise: [
    [5, 7],   // 左肩到左肘
    [7, 9],   // 左肘到左腕
    [6, 8],   // 右肩到右肘
    [8, 10],  // 右肘到右腕
    [5, 6],   // 肩部连接
    [5, 11],  // 左肩到左髋
    [6, 12]   // 右肩到右髋
  ],
  
  // 手指触摸动作
  finger_touch: [
    // 左手连接
    [91, 92], [92, 93], [93, 94], [94, 95],  // 拇指
    [91, 96], [96, 97], [97, 98], [98, 99],  // 食指
    [91, 100], [100, 101], [101, 102], [102, 103], // 中指
    // 右手连接
    [112, 113], [113, 114], [114, 115], [115, 116], // 拇指
    [112, 117], [117, 118], [118, 119], [119, 120], // 食指
    [112, 121], [121, 122], [122, 123], [123, 124], // 中指
    // 手腕到肘部
    [9, 91],   // 左腕到左手
    [10, 112]  // 右腕到右手
  ],
  
  // 手掌翻转动作
  palm_flip: [
    [7, 9],   // 左肘到左腕
    [8, 10],  // 右肘到右腕
    [9, 91],  // 左腕到左手
    [10, 112], // 右腕到右手
    // 手掌关键连接
    [91, 96], [91, 100], [91, 104], [91, 108], // 左手腕到手指
    [112, 117], [112, 121], [112, 125], [112, 129] // 右手腕到手指
  ]
}

/**
 * 关键点名称映射（用于调试和显示）
 */
export const KEYPOINT_NAMES = {
  // 身体关键点 (0-16)
  0: '鼻子', 1: '左眼', 2: '右眼', 3: '左耳', 4: '右耳',
  5: '左肩', 6: '右肩', 7: '左肘', 8: '右肘', 9: '左腕', 10: '右腕',
  11: '左髋', 12: '右髋', 13: '左膝', 14: '右膝', 15: '左踝', 16: '右踝',
  
  // 面部关键点 (17-84) - 简化显示
  17: '面部轮廓1', 84: '面部轮廓68',
  
  // 左手关键点 (91-111)
  91: '左手腕', 92: '左拇指1', 95: '左拇指4',
  96: '左食指1', 99: '左食指4',
  100: '左中指1', 103: '左中指4',
  
  // 右手关键点 (112-132)
  112: '右手腕', 113: '右拇指1', 116: '右拇指4',
  117: '右食指1', 120: '右食指4',
  121: '右中指1', 124: '右中指4'
}

/**
 * 根据需要的关键点索引获取对应的骨架连接
 * @param {Array} requiredKeypoints - 需要显示的关键点索引数组
 * @param {string} actionType - 动作类型（可选）
 * @param {boolean} includeHands - 是否包含手部连接（默认true）
 * @returns {Array} 骨架连接数组
 */
export function getSkeletonConnections(requiredKeypoints = [], actionType = null, includeHands = true) {
  if (requiredKeypoints.length === 0) {
    return includeHands ? FULL_SKELETON_CONNECTIONS : BODY_SKELETON_CONNECTIONS
  }

  // 如果指定了动作类型，优先使用动作特定连接
  if (actionType && ACTION_SPECIFIC_CONNECTIONS[actionType]) {
    return filterConnectionsByKeypoints(
      ACTION_SPECIFIC_CONNECTIONS[actionType],
      requiredKeypoints
    )
  }

  // 根据关键点范围智能选择连接
  let connections = []

  if (includeHands) {
    // 使用完整连接
    connections = [...FULL_SKELETON_CONNECTIONS]
  } else {
    // 只使用身体连接
    connections = [...BODY_SKELETON_CONNECTIONS]
  }

  // 过滤出实际存在的关键点连接
  return filterConnectionsByKeypoints(connections, requiredKeypoints)
}

/**
 * 检查是否包含身体关键点
 */
function hasBodyKeypoints(keypoints) {
  return keypoints.some(index => index >= 0 && index <= 16)
}

/**
 * 检查是否包含左手关键点
 */
function hasLeftHandKeypoints(keypoints) {
  return keypoints.some(index => index >= 91 && index <= 111)
}

/**
 * 检查是否包含右手关键点
 */
function hasRightHandKeypoints(keypoints) {
  return keypoints.some(index => index >= 112 && index <= 132)
}

/**
 * 获取手部连接
 */
function getHandConnections(hand) {
  if (hand === 'left') {
    return [
      [91, 92], [91, 96], [91, 100], [91, 104], [91, 108],
      [92, 93], [93, 94], [94, 95],
      [96, 97], [97, 98], [98, 99],
      [100, 101], [101, 102], [102, 103],
      [104, 105], [105, 106], [106, 107],
      [108, 109], [109, 110], [110, 111]
    ]
  } else {
    return [
      [112, 113], [112, 117], [112, 121], [112, 125], [112, 129],
      [113, 114], [114, 115], [115, 116],
      [117, 118], [118, 119], [119, 120],
      [121, 122], [122, 123], [123, 124],
      [125, 126], [126, 127], [127, 128],
      [129, 130], [130, 131], [131, 132]
    ]
  }
}

/**
 * 根据关键点过滤连接
 */
function filterConnectionsByKeypoints(connections, keypoints) {
  const keypointSet = new Set(keypoints)

  return connections.filter(connection => {
    // 支持新的连接对象格式和旧的数组格式
    const start = connection.start !== undefined ? connection.start : connection[0]
    const end = connection.end !== undefined ? connection.end : connection[1]

    return keypointSet.has(start) && keypointSet.has(end)
  })
}

/**
 * RGB颜色数组转换为十六进制颜色字符串
 * @param {Array} rgbArray - RGB颜色数组 [r, g, b]
 * @returns {string} 十六进制颜色字符串
 */
export function rgbToHex(rgbArray) {
  const [r, g, b] = rgbArray
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
}

/**
 * 获取关键点的颜色配置
 * @param {number} keypointIndex - 关键点索引
 * @returns {string} 颜色值
 */
export function getKeypointColor(keypointIndex) {
  const keypointInfo = KEYPOINT_INFO[keypointIndex]
  if (keypointInfo && keypointInfo.color) {
    return rgbToHex(keypointInfo.color)
  }

  return '#6b7280' // 默认灰色
}

/**
 * 获取连接线的颜色配置
 * @param {Object|Array} connection - 连接对象 {start, end, color} 或数组 [start, end]
 * @returns {string} 颜色值
 */
export function getConnectionColor(connection) {
  // 如果连接对象包含颜色信息，直接使用
  if (connection.color) {
    return rgbToHex(connection.color)
  }

  // 兼容旧格式的数组连接
  const [start, end] = Array.isArray(connection) ? connection : [connection.start, connection.end]

  // 身体连接 - 绿色
  if ((start <= 22 && end <= 22)) {
    return '#22c55e'
  }

  // 手部连接 - 橙色
  if ((start >= 91 && end >= 91)) {
    return '#f97316'
  }

  return '#6b7280' // 默认灰色
}

/**
 * 获取动作相关的关键点索引
 * @param {string} actionType - 动作类型
 * @returns {Array} 关键点索引数组
 */
export function getActionKeypoints(actionType) {
  const actionKeypointMap = {
    shoulder_touch: [0, 5, 6, 7, 8, 9, 10],
    arm_raise: [5, 6, 7, 8, 9, 10, 11, 12],
    finger_touch: [9, 10, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124],
    palm_flip: [7, 8, 9, 10, 91, 96, 100, 104, 108, 112, 117, 121, 125, 129]
  }
  
  return actionKeypointMap[actionType] || []
}
