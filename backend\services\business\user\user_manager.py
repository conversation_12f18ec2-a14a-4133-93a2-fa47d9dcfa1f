"""
智能康复系统 - 用户管理服务
实现用户身份验证、任务数据加载和管理功能
"""
import json
import time
import logging
import uuid
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime, timezone
from models.data_models import UserInfo, ActionInfo, CurrentAction, TrainingSession
from models.constants import SystemConfig

class UserManager:
    """用户管理器"""
    
    def __init__(self):
        """初始化用户管理器"""
        self.logger = logging.getLogger(__name__)
        
        # 数据文件路径
        self.users_file = Path(__file__).parent.parent.parent.parent / "data" / "users.json"
        self.actions_file = Path(__file__).parent.parent.parent.parent / "data" / "tasks_template.json"
        
        # 内存缓存
        self.users_cache: Dict[str, Dict[str, Any]] = {}
        self.actions_cache: Dict[str, Dict[str, Any]] = {}
        
        # 当前活跃会话
        self.active_sessions: Dict[str, TrainingSession] = {}
        
        # 加载数据
        self._load_users()
        self._load_actions()

        self.logger.info("用户管理器初始化完成")
    
    def _load_users(self):
        """加载用户数据"""
        try:
            if self.users_file.exists():
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.users_cache = data.get('users', {})
                self.logger.info(f"加载用户数据: {len(self.users_cache)} 个用户")
            else:
                self.logger.warning("用户数据文件不存在")
                self.users_cache = {}
        except Exception as e:
            self.logger.error(f"加载用户数据失败: {e}")
            self.users_cache = {}

    def _load_actions(self):
        """加载任务模板数据"""
        try:
            if self.actions_file.exists():
                with open(self.actions_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.actions_cache = data.get('task_templates', {})
                self.logger.info(f"加载任务模板: {len(self.actions_cache)} 个模板")
            else:
                self.logger.warning("任务模板文件不存在")
                self.actions_cache = {}
        except Exception as e:
            self.logger.error(f"加载任务模板失败: {e}")
            self.actions_cache = {}
    
    def _save_users(self):
        """保存用户数据"""
        try:
            # 确保目录存在
            self.users_file.parent.mkdir(parents=True, exist_ok=True)
            
            data = {
                'users': self.users_cache,
                'metadata': {
                    'version': '1.0',
                    'last_updated': datetime.now(timezone.utc).isoformat(),
                    'total_users': len(self.users_cache),
                    'active_users': len([u for u in self.users_cache.values() if u.get('last_login')])
                }
            }
            
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            self.logger.info("用户数据保存成功")
        except Exception as e:
            self.logger.error(f"保存用户数据失败: {e}")
    
    def authenticate_user(self, patient_id: str) -> Optional[UserInfo]:
        """
        用户身份验证
        Args:
            patient_id: 患者ID
        Returns:
            UserInfo: 用户信息，验证失败返回None
        """
        try:
            if patient_id not in self.users_cache:
                self.logger.warning(f"用户不存在: {patient_id}")
                return None
            
            user_data = self.users_cache[patient_id]
            # 更新最后登录时间（转换为ISO格式字符串）
            user_data['last_login'] = datetime.now(timezone.utc).isoformat()
            self._save_users()
            # 将ISO格式字符串转换为datetime对象
            last_login_str = user_data['last_login']
            last_login_datetime = datetime.fromisoformat(last_login_str) if last_login_str else None
            
            # 创建UserInfo对象
            user_info = UserInfo(
                patient_id=user_data['patient_id'],
                name=user_data['name'],
                age=user_data.get('age'),
                gender=user_data.get('gender'),
                last_login=last_login_datetime
            )
            self.logger.info(f"用户验证成功: {patient_id} - {user_data['name']}")
            return user_info
            
        except Exception as e:
            self.logger.error(f"用户验证失败: {e}")
            return None
    
    def get_user_actions(self, patient_id: str) -> List[ActionInfo]:
        """
        获取用户分配的任务
        
        Args:
            patient_id: 患者ID
            
        Returns:
            List[actionInfo]: 任务列表
        """
        try:
            if patient_id not in self.users_cache:
                self.logger.warning(f"用户不存在: {patient_id}")
                return []
            user_data = self.users_cache[patient_id]
            assigned_actions = user_data.get('assigned_tasks', [])
            actions = []
            for action_name in assigned_actions:
                # 首先尝试直接匹配任务ID
                if action_name in self.actions_cache:
                    action_template = self.actions_cache[action_name]
                    # 创建ActionInfo对象
                    action_info = ActionInfo(
                        action_type=action_template['action_type'],
                        side=action_template['side'],
                        required_keypoints=action_template['required_keypoints']
                    )
                    actions.append(action_info)
                else:
                    # 如果直接匹配失败，尝试根据动作类型查找
                    found_templates = []
                    for template_id, template in self.actions_cache.items():
                        if template.get('action_type') == action_name:
                            found_templates.append((template_id, template))
                    if found_templates:
                        # 如果找到多个模板，选择第一个（可以根据需要调整选择逻辑）
                        template_id, action_template = found_templates[0]
                        self.logger.info(f"根据动作类型 {action_name} 找到任务模板: {template_id}")
                        # 创建ActionInfo对象
                        action_info = ActionInfo(
                            action_type=action_template['action_type'],
                            side=action_template['side'],
                            required_keypoints=action_template['required_keypoints']
                        )
                        actions.append(action_info)
                    else:
                        self.logger.warning(f"任务模板不存在: {action_name}")
            
            self.logger.info(f"获取用户任务: {patient_id} - {len(actions)} 个任务")
            return actions
            
        except Exception as e:
            self.logger.error(f"获取用户任务失败: {e}")
            return []

   

# 全局用户管理器实例
user_manager = UserManager()
