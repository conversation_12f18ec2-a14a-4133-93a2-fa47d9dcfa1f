# 用户数据文件说明

## 文件结构

### users.json
主要的用户数据文件，包含正式的用户信息。

### test_users.json
测试用户数据文件，用于开发和测试。

## 用户数据格式

```json
{
  "users": {
    "用户ID": {
      "patient_id": "患者ID",
      "name": "用户姓名",
      "age": 年龄,
      "gender": "性别 (male/female)",
      "diagnosis": "诊断信息",
      "created_at": "创建时间 (ISO格式)",
      "assigned_tasks": ["分配的任务类型数组"],
      "last_login": "最后登录时间 (ISO格式) 或 null"
    }
  },
  "metadata": {
    "version": "版本号",
    "last_updated": "最后更新时间",
    "total_users": 总用户数,
    "active_users": 活跃用户数,
    "description": "描述信息"
  }
}
```

## 支持的任务类型

- `shoulder_touch`: 摸肩膀动作
- `arm_raise`: 手臂上抬动作
- `finger_touch`: 对指动作
- `palm_flip`: 手掌翻转动作

## 测试用户列表

### 正式用户 (users.json)
- **P001**: 张三 - 肩周炎患者，分配肩膀和手臂训练
- **P002**: 李四 - 手部功能障碍，分配手部精细动作训练
- **P003**: 王五 - 上肢运动功能障碍，分配综合训练
- **P004**: 赵六 - 手腕功能恢复，分配手部训练
- **P005**: 孙七 - 肩关节活动受限，分配全面训练

### 测试用户 (test_users.json)
- **TEST001-004**: 单一任务类型测试用户
- **DEMO001**: 演示用户，包含所有任务类型

## 使用说明

1. 系统启动时会自动加载 `users.json` 文件
2. 用户登录时使用 `patient_id` 进行身份验证
3. 系统会根据 `assigned_tasks` 为用户加载对应的训练任务
4. `last_login` 字段会在用户成功登录时自动更新

## 注意事项

- 所有时间字段使用 ISO 8601 格式
- `assigned_tasks` 中的任务类型必须在 `tasks_template.json` 中有对应的模板
- 用户数据修改后需要重启系统才能生效
