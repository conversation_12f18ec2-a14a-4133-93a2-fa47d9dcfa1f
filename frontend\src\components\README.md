# Components Directory

This directory contains reusable Vue components organized by category:

## Structure
- `common/` - Common UI components (buttons, modals, etc.)
- `training/` - Training-specific components
- `visualization/` - Data visualization components
- `report/` - Report-related components

## Naming Convention
- Use PascalCase for component names
- Use descriptive names that indicate the component's purpose
- Keep component files under 350 lines as per project rules
