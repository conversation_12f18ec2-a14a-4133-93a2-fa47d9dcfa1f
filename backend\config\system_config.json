{"server": {"host": "0.0.0.0", "port": 5000, "debug": false}, "logging": {"level": "INFO", "file_enabled": true, "console_enabled": true, "max_file_size": "10MB", "backup_count": 5, "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "date_format": "%Y-%m-%d %H:%M:%S"}, "zmq": {"detect_port": 6070, "camera_port": 6080, "timeout": 5000, "retry_count": 3}, "video_stream": {"enabled": true, "quality": 80, "max_fps": 30, "buffer_size": 10}, "websocket": {"enabled": true, "cors_origins": "*", "ping_timeout": 60, "ping_interval": 25}, "business": {"user_detection_threshold": 3, "action_recognition_threshold": 0.8, "min_score_threshold": 60, "preparation_duration": 3.0}, "data_paths": {"users_file": "data/users.json", "tasks_file": "data/tasks_template.json", "logs_dir": "logs", "temp_dir": "temp"}}