<template>
  <div class="keypoint-canvas-container" ref="containerRef">
    <canvas 
      ref="canvasRef"
      class="keypoint-canvas"
      :class="canvasClasses"
    ></canvas>
    
    <!-- 控制面板 -->
    <div v-if="showControls" class="canvas-controls">
      <div class="controls-panel">
        <el-button-group size="small">
          <el-button 
            :type="isRendering ? 'danger' : 'primary'"
            @click="toggleRendering"
          >
            <el-icon><component :is="isRendering ? 'VideoPause' : 'VideoPlay'" /></el-icon>
            {{ isRendering ? '暂停' : '开始' }}
          </el-button>
          
          <el-button @click="clearCanvas">
            <el-icon><Delete /></el-icon>
            清空
          </el-button>
          
          <el-button @click="resetView">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-button-group>
        
        <!-- 渲染选项 -->
        <div class="render-options">
          <el-checkbox 
            v-model="renderOptions.showConfidence"
            @change="updateRenderOptions"
            size="small"
          >
            显示置信度
          </el-checkbox>
          
          <el-checkbox
            v-model="renderOptions.enableSmoothing"
            @change="updateRenderOptions"
            size="small"
          >
            平滑处理
          </el-checkbox>

          <el-checkbox
            v-model="renderOptions.showHands"
            @change="updateRenderOptions"
            size="small"
          >
            显示手部
          </el-checkbox>
        </div>
      </div>
      
      <!-- 统计信息 -->
      <div v-if="showStats" class="stats-panel">
        <div class="stat-item">
          <span class="stat-label">关键点:</span>
          <span class="stat-value">{{ stats.visibleKeypointCount }}/{{ stats.keypointCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">FPS:</span>
          <span class="stat-value">{{ stats.fps }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">缩放:</span>
          <span class="stat-value">{{ (stats.scale * 100).toFixed(0) }}%</span>
        </div>
      </div>
    </div>
    
    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-overlay">
      <el-icon class="loading-icon" :size="24">
        <Loading />
      </el-icon>
      <span class="loading-text">初始化渲染器...</span>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { KeypointRenderer } from '@/utils/keypointRenderer'
import { useMainStore } from '@/stores/main'
import { 
  VideoPlay, 
  VideoPause, 
  Delete, 
  Refresh, 
  Loading 
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  width: {
    type: [Number, String],
    default: '100%'
  },
  height: {
    type: [Number, String],
    default: '400px'
  },
  showControls: {
    type: Boolean,
    default: true
  },
  showStats: {
    type: Boolean,
    default: true
  },
  autoStart: {
    type: Boolean,
    default: true
  },
  renderOptions: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['ready', 'error', 'stats-update'])

// 模板引用
const containerRef = ref(null)
const canvasRef = ref(null)

// 状态管理 (使用主store)
const mainStore = useMainStore()

// 关键点数据
const keypointData = computed(() => mainStore.poseKeypoints || [])
const requiredKeypoints = computed(() => {
  // 从mainStore获取当前动作需要的关键点，如果没有则使用所有身体关键点
  const currentAction = mainStore.currentAction
  if (currentAction && currentAction.action_info && currentAction.action_info.required_keypoints) {
    return currentAction.action_info.required_keypoints
  }

  // 默认显示身体主要关键点 (0-22)
  return Array.from({ length: 23 }, (_, i) => i)
})
const hasKeypointData = computed(() => keypointData.value.length > 0)

// 更新关键点数据的方法
const updateKeypointData = (data) => {
  // 可以通过主store更新数据
  console.log('更新关键点数据:', data)
}

// 组件状态
const isLoading = ref(true)
const isRendering = ref(false)
const renderer = ref(null)
const stats = reactive({
  keypointCount: 0,
  visibleKeypointCount: 0,
  fps: 0,
  scale: 1
})

// 渲染选项
const renderOptions = reactive({
  pointRadius: 4,
  pointColor: '#ff4444',
  lineColor: '#00ff00',
  lineWidth: 2,
  confidenceThreshold: 0.3,
  showConfidence: false,
  enableSmoothing: true,
  showHands: true,
  maxFPS: 60,
  ...props.renderOptions
})

// 计算属性
const canvasClasses = computed(() => ({
  'rendering': isRendering.value,
  'has-data': hasKeypointData.value,
  'loading': isLoading.value
}))

const canvasStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}))

// 监听关键点数据变化
watch([keypointData, requiredKeypoints], ([newKeypoints, newRequired]) => {
  if (renderer.value && newKeypoints.length > 0) {
    renderer.value.updateKeypoints(newKeypoints, newRequired)
    updateStats()
  }
}, { deep: true })

// 监听渲染选项变化
watch(renderOptions, (newOptions) => {
  if (renderer.value) {
    renderer.value.updateOptions(newOptions)
  }
}, { deep: true })

// 生命周期
onMounted(async () => {
  await nextTick()
  await initializeRenderer()
})

onUnmounted(() => {
  destroyRenderer()
})

// 方法
const initializeRenderer = async () => {
  try {
    isLoading.value = true
    
    if (!canvasRef.value) {
      throw new Error('Canvas元素未找到')
    }
    
    // 设置Canvas尺寸
    setupCanvasSize()
    
    // 创建渲染器
    renderer.value = new KeypointRenderer(canvasRef.value, renderOptions)
    
    // 如果有数据，立即渲染
    if (hasKeypointData.value) {
      renderer.value.updateKeypoints(keypointData.value, requiredKeypoints.value)
    }
    
    // 自动开始渲染
    if (props.autoStart) {
      startRendering()
    }
    
    updateStats()
    emit('ready', renderer.value)
    
  } catch (error) {
    console.error('渲染器初始化失败:', error)
    emit('error', error)
  } finally {
    isLoading.value = false
  }
}

const setupCanvasSize = () => {
  if (!canvasRef.value || !containerRef.value) return
  
  const container = containerRef.value
  const canvas = canvasRef.value
  
  // 获取容器尺寸
  const rect = container.getBoundingClientRect()
  const dpr = window.devicePixelRatio || 1
  
  // 设置Canvas尺寸
  canvas.width = rect.width * dpr
  canvas.height = rect.height * dpr
  
  // 设置CSS尺寸
  canvas.style.width = rect.width + 'px'
  canvas.style.height = rect.height + 'px'
}

const startRendering = () => {
  if (renderer.value && !isRendering.value) {
    renderer.value.startRenderLoop()
    isRendering.value = true
  }
}

const stopRendering = () => {
  if (renderer.value && isRendering.value) {
    renderer.value.stopRenderLoop()
    isRendering.value = false
  }
}

const toggleRendering = () => {
  if (isRendering.value) {
    stopRendering()
  } else {
    startRendering()
  }
}

const clearCanvas = () => {
  if (renderer.value) {
    renderer.value.clear()
  }
}

const resetView = () => {
  if (renderer.value) {
    renderer.value.updateTransform()
    updateStats()
  }
}

const updateRenderOptions = () => {
  if (renderer.value) {
    renderer.value.updateOptions(renderOptions)
  }
}

const updateStats = () => {
  if (renderer.value) {
    const rendererStats = renderer.value.getStats()
    Object.assign(stats, rendererStats)
    emit('stats-update', stats)
  }
}

const destroyRenderer = () => {
  if (renderer.value) {
    renderer.value.destroy()
    renderer.value = null
  }
  isRendering.value = false
}

// 暴露方法给父组件
defineExpose({
  startRendering,
  stopRendering,
  clearCanvas,
  resetView,
  updateRenderOptions,
  getStats: () => stats,
  getRenderer: () => renderer.value
})
</script>

<style scoped>
.keypoint-canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.keypoint-canvas {
  display: block;
  width: 100%;
  height: 100%;
  background: #ffffff;
  transition: opacity 0.3s ease;
}

.keypoint-canvas.loading {
  opacity: 0.5;
}

.keypoint-canvas.rendering {
  cursor: crosshair;
}

.keypoint-canvas.has-data {
  border: 2px solid #10b981;
}

.canvas-controls {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  pointer-events: none;
}

.controls-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  pointer-events: auto;
}

.render-options {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stats-panel {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 12px;
  min-width: 120px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.stat-item:last-child {
  margin-bottom: 0;
}

.stat-label {
  opacity: 0.8;
}

.stat-value {
  font-weight: 600;
  color: #10b981;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.loading-icon {
  color: #3b82f6;
  animation: spin 1s linear infinite;
}

.loading-text {
  color: #6b7280;
  font-size: 14px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .canvas-controls {
    flex-direction: column;
    gap: 8px;
  }
  
  .controls-panel {
    width: 100%;
  }
  
  .render-options {
    flex-direction: row;
    gap: 12px;
  }
  
  .stats-panel {
    width: 100%;
  }
}
</style>
