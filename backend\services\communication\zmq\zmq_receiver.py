"""
智能康复系统 - ZMQ数据接收器
简化版本，确保在Flask应用上下文中处理数据
"""
import zmq
import json
import logging
import time
from typing import Optional, Dict, Any, Tuple
from models.data_models import ZMQDetectData, ZMQCameraFrame, PoseDataConverter
import numpy as np
import cv2
class ZMQReceiver:
    """ZMQ数据接收器"""
    
    def __init__(self):
        """初始化ZMQ接收器"""
        self.logger = logging.getLogger(__name__)
        self.context = None
        self.detect_socket = None
        self.camera_socket = None
        self.latest_detect_data = None
        self.latest_camera_frame = None
        self.frontend_connected = False
        self.system_coordinator = None
        
    def initialize(self):
        """初始化ZMQ连接"""
        try:
            self.context = zmq.Context()
            
            # 检测数据接收器
            self.detect_socket = self.context.socket(zmq.SUB)
            self.detect_socket.connect("tcp://localhost:6070")
            self.detect_socket.setsockopt(zmq.SUBSCRIBE, b"detect_data")
            self.detect_socket.setsockopt(zmq.RCVTIMEO, 100)
            
            # 摄像头数据接收器
            self.camera_socket = self.context.socket(zmq.SUB)
            self.camera_socket.connect("tcp://localhost:6080")
            self.camera_socket.setsockopt(zmq.SUBSCRIBE, b"camera_frame")
            self.camera_socket.setsockopt(zmq.RCVTIMEO, 100)
            
            self.logger.info("ZMQ接收器初始化完成")
            
        except Exception as e:
            self.logger.error(f"ZMQ接收器初始化失败: {e}")
            raise
    
    def set_frontend_connected(self, connected: bool):
        """设置前端连接状态"""
        self.frontend_connected = connected
        self.logger.info(f"前端连接状态更新: {connected}")
    
    def set_system_coordinator(self, coordinator):
        """设置系统协调器引用"""
        self.system_coordinator = coordinator


    def start_data_processing(self):
        self.logger.info("开始ZMQ数据处理循环")
        if not self.context:
            self.initialize()
        # 添加调试信息
        self.logger.info(f"前端连接状态: {self.frontend_connected}")
        self.logger.info(f"系统协调器状态: {self.system_coordinator is not None}")
        while True:
            try:
                # # 只有在前端连接时才处理数据
                if not self.frontend_connected:
                    self.logger.info("前端未连接，等待连接...")
                    time.sleep(0.1)
                    continue
                # 接收检测数据
                self.get_latest_data()
                detect_data = self.latest_detect_data
                camera_data = self.latest_camera_frame
       
                # 如果有数据，传递给系统协调器处理
                if detect_data is not None and camera_data is not None and self.system_coordinator:
                    self.system_coordinator.handle_zmq_data(camera_data, detect_data)
                elif not self.system_coordinator:
                    self.logger.warning("⚠️ 系统协调器未设置")
                elif detect_data is None or camera_data is None:
                    self.logger.debug(f"📊 数据状态 - 检测数据: {detect_data is not None}, 摄像头数据: {camera_data is not None}")
                
                time.sleep(0.01)  # 避免CPU占用过高
                
            except Exception as e:
                self.logger.error(f"数据处理循环异常: {e}")
                time.sleep(0.1)
    def _receive_multipart_data(self, socket):
        """
        [重构的核心] 一个通用的、健壮的辅助函数，用于接收和解析 [topic, json] 格式的消息。
        """
        try:
            msg_parts = socket.recv_multipart(flags=zmq.NOBLOCK)
            if len(msg_parts) == 2:
                topic_bytes, json_payload_bytes = msg_parts
                payload_str = json_payload_bytes.decode('utf-8')
                if payload_str:
                    try:
                        data_dict = json.loads(payload_str)
                        return data_dict # 成功，返回解析后的数据
                    except json.JSONDecodeError:
                        self.logger.warning(f"收到的消息不是有效的JSON: '{payload_str[:50]}...'")
            else:
                self.logger.warning(f"接收到的消息格式不正确，期望2部分，实际{len(msg_parts)}部分。")
        except zmq.Again:
            pass # 没有新消息，是正常情况
        except Exception as e:
            self.logger.error(f"从socket接收数据时出错: {e}")

    def get_latest_data(self):
        detect_update = self._receive_multipart_data(self.detect_socket)
        if detect_update is not None:
            detect_data = PoseDataConverter.convert_zmq_detect_data(detect_update)
            self.latest_detect_data = detect_data
        self._process_camera_data_optimized()

    def _process_camera_data_optimized(self):
        """
        [新增] 优化的摄像头数据处理：
        1. 跳过积压的旧帧，只处理最新帧
        2. 优化图像解码性能
        """
        latest_camera_data = None
        frame_count = 0
        # 快速消费所有积压的摄像头数据，只保留最新的
        while True:
            camera_update = self._receive_multipart_data(self.camera_socket)
            if camera_update is None:
                break
            latest_camera_data = camera_update
        # 只处理最新的摄像头帧
        if latest_camera_data is not None and 'frame_data' in latest_camera_data:
            try:
                # 性能优化：使用更高效的图像解码方式
                frame_hex = latest_camera_data['frame_data']
                if frame_hex:  # 确保不是空字符串
                    frame_bytes = bytes.fromhex(frame_hex)
                    frame_array = np.frombuffer(frame_bytes, dtype=np.uint8)
                    
                    # 使用 IMREAD_COLOR 保持彩色，但优化解码
                    frame = cv2.imdecode(frame_array, cv2.IMREAD_COLOR)
                    if frame is not None and frame.size > 0:
                        self.latest_camera_frame = frame
                    else:
                        self.logger.warning("图像解码失败")
                        
            except ValueError as e:
                self.logger.error(f"Hex解码失败: {e}")
            except Exception as e:
                self.logger.error(f"处理摄像头帧时出错: {e}")
    
    def cleanup(self):
        """清理资源"""
        if self.detect_socket:
            self.detect_socket.close()
        if self.camera_socket:
            self.camera_socket.close()
        if self.context:
            self.context.term()
        self.logger.info("ZMQ接收器资源已清理")

# 创建单例实例
zmq_receiver = ZMQReceiver()