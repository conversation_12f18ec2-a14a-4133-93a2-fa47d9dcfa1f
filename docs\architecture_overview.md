# 智能康复系统整体架构文档

## 概述

智能康复系统是一个基于计算机视觉和人工智能技术的实时康复训练平台。系统采用前后端分离的架构设计，通过WebSocket实现实时双向通信，结合ZeroMQ进行高性能数据传输，使用状态机模式管理复杂的业务流程。

## 系统特性

### 核心功能
- **实时姿态检测**：基于RTMPose模型的133关键点人体姿态识别
- **智能动作评估**：实时动作标准性评分和反馈
- **个性化训练**：根据用户情况定制训练方案
- **训练报告生成**：详细的训练数据分析和建议
- **多用户管理**：支持用户识别和会话管理

### 技术特点
- **高实时性**：30-60 FPS的实时数据处理和反馈
- **高可靠性**：完善的错误处理和状态恢复机制
- **可扩展性**：模块化设计，易于功能扩展
- **跨平台**：支持多种操作系统和浏览器

## 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "前端层 (Frontend)"
        A[Vue 3 应用]
        B[Pinia 状态管理]
        C[Socket.IO 客户端]
        D[Element Plus UI]
        E[TailwindCSS 样式]
    end

    subgraph "通信层 (Communication)"
        F[WebSocket 连接]
        G[HTTP API]
    end

    subgraph "后端层 (Backend)"
        H[Flask 应用服务器]
        I[Socket.IO 服务端]
        J[系统协调器]
        K[状态管理器]
        L[状态处理器工厂]
    end

    subgraph "业务逻辑层 (Business Logic)"
        M[用户检测服务]
        N[动作评估服务]
        O[训练报告服务]
        P[数据验证服务]
    end

    subgraph "数据层 (Data Layer)"
        Q[ZMQ 数据接收器]
        R[数据序列化器]
        S[数据模型]
    end

    subgraph "计算机视觉层 (Computer Vision)"
        T[RTMPose 姿态检测]
        U[摄像头数据采集]
        V[图像预处理]
    end

    A --> B
    A --> C
    A --> D
    A --> E
    C --> F
    F --> I
    I --> H
    H --> J
    J --> K
    K --> L
    J --> M
    J --> N
    J --> O
    J --> P
    J --> Q
    Q --> R
    R --> S
    Q --> T
    T --> U
    T --> V
```

### 技术栈

#### 前端技术栈
| 技术 | 版本 | 用途 |
|------|------|------|
| Vue 3 | 3.x | 前端框架 |
| Vite | 4.x | 构建工具 |
| Pinia | 2.x | 状态管理 |
| Element Plus | 2.x | UI组件库 |
| TailwindCSS | 3.x | CSS框架 |
| Socket.IO Client | 4.x | WebSocket通信 |

#### 后端技术栈
| 技术 | 版本 | 用途 |
|------|------|------|
| Python | 3.8+ | 编程语言 |
| Flask | 2.x | Web框架 |
| Flask-SocketIO | 5.x | WebSocket支持 |
| ZeroMQ | 4.x | 高性能消息队列 |
| OpenCV | 4.x | 计算机视觉 |
| RTMPose | - | 姿态检测模型 |

#### 系统依赖
| 组件 | 用途 | 配置 |
|------|------|------|
| Redis | 会话存储 | 可选 |
| SQLite | 数据存储 | 默认 |
| Nginx | 反向代理 | 生产环境 |

## 核心组件详解

### 1. 前端架构

#### 组件层次结构

```
src/
├── components/          # 可复用组件
│   ├── common/         # 通用组件
│   ├── training/       # 训练相关组件
│   └── report/         # 报告相关组件
├── views/              # 页面组件
│   ├── Login.vue       # 登录页面
│   ├── TaskIntroduction.vue  # 任务介绍
│   ├── Training.vue    # 训练页面
│   └── Report.vue      # 报告页面
├── stores/             # Pinia状态管理
│   ├── main.js         # 主状态store
│   └── index.js        # store入口
├── services/           # 服务层
│   └── websocket.js    # WebSocket服务
├── router/             # 路由配置
│   └── index.js        # 路由定义
└── utils/              # 工具函数
```

#### 状态管理架构

```javascript
// 主状态store结构
const useMainStore = defineStore('main', () => {
  // 系统状态
  const currentState = ref('waiting')
  const userInfo = ref(null)
  const currentAction = ref(null)
  const actionList = ref([])

  // 实时数据
  const frameData = ref(null)
  const poseKeypoints = ref([])
  const frameCount = ref(0)

  // 连接状态
  const isConnected = ref(false)
  const connectionError = ref(null)

  // 暂停状态
  const isPaused = ref(false)
  const pauseReason = ref(null)

  // 统一消息处理器
  const handleMessage = (message) => {
    // 根据消息类型分发处理
    switch (message.message_type) {
      case 'waiting_message':
        handleWaitingMessage(message.data)
        break
      case 'login_success':
        handleLoginSuccess(message.data)
        break
      // ... 其他消息类型
    }
  }

  return {
    // 状态和方法导出
    currentState,
    userInfo,
    handleMessage,
    // ...
  }
})
```

### 2. 后端架构

#### 应用工厂模式

```python
def create_app(config=None):
    """应用工厂函数"""
    app = Flask(__name__)

    # 基础配置
    app.config.update({
        'SECRET_KEY': 'rehabilitation_system_secret_key',
        'DEBUG': False
    })

    # 初始化扩展
    init_extensions(app)

    # 注册路由
    _register_routes(app)

    # 初始化组件
    _initialize_components(app)

    return app

def _initialize_components(app):
    """初始化系统组件"""
    # 初始化WebSocket管理器
    websocket_manager.initialize(app, socketio)

    # 建立组件间连接
    websocket_manager.set_system_coordinator(system_coordinator)
    system_coordinator.set_websocket_manager(websocket_manager)

    # 设置ZMQ接收器
    zmq_receiver.set_system_coordinator(system_coordinator)
    websocket_manager.zmq_receiver = zmq_receiver
```

#### 系统协调器

```python
class SystemCoordinator:
    """系统协调器 - 核心业务逻辑控制"""

    def __init__(self):
        self.state_manager = state_manager
        self.handler_factory = state_handler_factory
        self.websocket_manager = None
        self.frontend_connected = False
        self.context = {
            "current_time": time.time(),
            "session_active": False
        }

    def handle_zmq_data(self, camera_frame, detect_data):
        """统一处理ZMQ数据"""
        # 更新上下文时间
        self.context["current_time"] = time.time()

        # 获取当前状态处理器
        current_state = self.state_manager.get_current_state()
        handler = self.handler_factory.get_handler(current_state)

        if not handler:
            self.logger.warning(f"未找到状态处理器: {current_state.value}")
            return

        # 进入状态
        handler.enter_state(self.context)

        # 处理数据
        result = handler.handle_data(detect_data, self.context)

        # 构建状态数据
        state_data = result.get("state_data")
        if state_data is None:
            state_data = SystemStateData(
                current_state=current_state,
                pose_keypoints=detect_data.pose_keypoints if detect_data else [],
                frame_data=self._encode_frame(camera_frame)
            )

        # 处理状态转换
        if result.get("trigger_event"):
            success = self.state_manager.transition_to(
                result["trigger_event"],
                **result.get("transition_data", {})
            )
            if success:
                self.logger.info(f"状态转换成功: {result['trigger_event'].value}")

        # 发送WebSocket消息
        if result.get("websocket_message") and self.websocket_manager:
            self.websocket_manager.send_state_message(
                result["websocket_message"],
                state_data
            )
```

### 3. 数据流架构

#### 数据流向图

```mermaid
sequenceDiagram
    participant CV as 计算机视觉模块
    participant ZMQ as ZMQ接收器
    participant SC as 系统协调器
    participant SM as 状态管理器
    participant SH as 状态处理器
    participant WS as WebSocket管理器
    participant FE as 前端应用

    CV->>ZMQ: 发送姿态数据和摄像头帧
    ZMQ->>SC: 转发数据到系统协调器
    SC->>SM: 获取当前状态
    SM-->>SC: 返回当前状态
    SC->>SH: 调用状态处理器处理数据
    SH->>SH: 执行业务逻辑
    SH-->>SC: 返回处理结果
    SC->>SM: 触发状态转换(如需要)
    SM-->>SC: 确认状态转换
    SC->>WS: 发送WebSocket消息
    WS->>FE: 推送实时数据
    FE->>FE: 更新UI和状态
    FE->>WS: 发送用户命令(如需要)
    WS->>SC: 转发用户命令
```

#### 数据模型层次

```python
# 核心数据模型
@dataclass
class SystemStateData:
    """系统状态数据 - 所有WebSocket消息的统一数据格式"""
    current_state: SystemState
    message: str = ""
    user_info: Optional[UserInfo] = None
    current_action: Optional[CurrentAction] = None
    action_list: List[ActionInfo] = field(default_factory=list)
    pose_keypoints: List[List[float]] = field(default_factory=list)
    frame_data: Optional[str] = None
    progress_info: Optional[Dict[str, Any]] = None
    statistics: Optional[Dict[str, Any]] = None
    session_id: Optional[str] = None
    state_history: List[str] = field(default_factory=list)

@dataclass
class ZMQDetectData:
    """ZMQ检测数据结构"""
    timestamp: float
    patient_id: str
    pose_keypoints: List[List[float]]  # 133个关键点，每个[x,y,confidence]
    pose_bbox: List[float]  # [x1, y1, x2, y2]
    face_bbox: List[float]  # [x1, y1, x2, y2]

@dataclass
class UserInfo:
    """用户信息"""
    patient_id: str
    name: str
    age: Optional[int] = None
    gender: Optional[str] = None
    last_login: Optional[datetime] = None

@dataclass
class CurrentAction:
    """当前动作信息"""
    action_type: ActionType  # "shoulder_touch", "arm_raise", etc.
    side: TrainingSide      # "left", "right"
    sets: int
    reps_per_set: int
    target_score: float
    current_set: int = 1
    current_rep: int = 0
    current_score: float = 0.0
    feedback: str = ""
    completion_rate: float = 0.0
    required_keypoints: List[int] = field(default_factory=list)
```

## 部署架构

### 开发环境部署

```mermaid
graph LR
    subgraph "开发机器"
        A[前端开发服务器<br/>Vite Dev Server<br/>:3000]
        B[后端Flask应用<br/>:5000]
        C[计算机视觉模块<br/>RTMPose]
        D[ZMQ消息队列<br/>:6070, :6080]
    end

    A <--> B
    C --> D
    D --> B
```

**启动顺序**:
1. 启动计算机视觉模块和ZMQ服务
2. 启动后端Flask应用
3. 启动前端开发服务器

### 生产环境部署

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[Nginx 负载均衡器<br/>:80, :443]
    end

    subgraph "应用服务器集群"
        APP1[Flask应用实例1<br/>:5001]
        APP2[Flask应用实例2<br/>:5002]
        APP3[Flask应用实例3<br/>:5003]
    end

    subgraph "静态资源服务"
        STATIC[Nginx 静态文件服务<br/>前端构建产物]
    end

    subgraph "计算机视觉服务"
        CV1[RTMPose服务1<br/>GPU节点1]
        CV2[RTMPose服务2<br/>GPU节点2]
    end

    subgraph "消息队列"
        ZMQ[ZMQ集群<br/>:6070-6079]
    end

    subgraph "数据存储"
        DB[(SQLite/PostgreSQL<br/>用户数据)]
        REDIS[(Redis<br/>会话缓存)]
    end

    LB --> STATIC
    LB --> APP1
    LB --> APP2
    LB --> APP3

    APP1 --> DB
    APP1 --> REDIS
    APP2 --> DB
    APP2 --> REDIS
    APP3 --> DB
    APP3 --> REDIS

    CV1 --> ZMQ
    CV2 --> ZMQ
    ZMQ --> APP1
    ZMQ --> APP2
    ZMQ --> APP3
```

### Docker容器化部署

```yaml
# docker-compose.yml
version: '3.8'

services:
  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    environment:
      - VITE_API_URL=http://backend:5000

  backend:
    build: ./backend
    ports:
      - "5000:5000"
    depends_on:
      - redis
      - zmq-broker
    environment:
      - FLASK_ENV=production
      - REDIS_URL=redis://redis:6379
      - ZMQ_DETECT_URL=tcp://zmq-broker:6070
      - ZMQ_CAMERA_URL=tcp://zmq-broker:6080
    volumes:
      - ./data:/app/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  zmq-broker:
    build: ./computer-vision
    ports:
      - "6070:6070"
      - "6080:6080"
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

volumes:
  redis_data:
```

## 性能优化

### 1. 前端性能优化

#### 代码分割和懒加载

```javascript
// 路由级别的代码分割
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue')
  },
  {
    path: '/training',
    name: 'Training',
    component: () => import('@/views/Training.vue')
  },
  {
    path: '/report',
    name: 'Report',
    component: () => import('@/views/Report.vue')
  }
]
```

#### 实时数据优化

```javascript
// 使用防抖优化高频数据更新
import { debounce } from 'lodash-es'

const updatePoseKeypoints = debounce((keypoints) => {
  poseKeypoints.value = keypoints
}, 16) // 约60FPS

// WebSocket消息处理优化
const handleMessage = (message) => {
  // 优先处理关键状态消息
  if (message.message_type.includes('_success') ||
      message.message_type.includes('_completed')) {
    handleCriticalMessage(message)
  } else {
    // 非关键消息使用requestAnimationFrame优化
    requestAnimationFrame(() => {
      handleNormalMessage(message)
    })
  }
}
```

#### 内存管理

```javascript
// 限制历史数据大小
const addToHistory = (item) => {
  notificationHistory.value.unshift(item)

  // 保持历史记录在合理范围内
  if (notificationHistory.value.length > 50) {
    notificationHistory.value = notificationHistory.value.slice(0, 25)
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (heartbeatTimer) {
    clearInterval(heartbeatTimer)
  }
  if (reconnectTimer) {
    clearTimeout(reconnectTimer)
  }
})
```

### 2. 后端性能优化

#### 异步处理优化

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class OptimizedSystemCoordinator:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.data_queue = asyncio.Queue(maxsize=100)

    async def handle_zmq_data_async(self, camera_frame, detect_data):
        """异步处理ZMQ数据"""
        try:
            # 将CPU密集型任务放到线程池
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self._process_pose_data,
                detect_data
            )

            # 异步发送WebSocket消息
            await self._send_websocket_message_async(result)

        except Exception as e:
            self.logger.error(f"异步处理数据失败: {e}")

    def _process_pose_data(self, detect_data):
        """CPU密集型的姿态数据处理"""
        # 姿态分析和评分逻辑
        return processed_result
```

#### 数据缓存策略

```python
from functools import lru_cache
import time

class CachedUserDetectionService:
    def __init__(self):
        self.user_cache = {}
        self.cache_ttl = 5.0  # 5秒缓存

    @lru_cache(maxsize=128)
    def get_user_template(self, patient_id: str):
        """缓存用户模板数据"""
        return self._load_user_template(patient_id)

    def detect_user_with_cache(self, pose_keypoints, timestamp):
        """带缓存的用户检测"""
        cache_key = self._generate_cache_key(pose_keypoints)

        if cache_key in self.user_cache:
            cached_result, cache_time = self.user_cache[cache_key]
            if timestamp - cache_time < self.cache_ttl:
                return cached_result

        # 执行实际检测
        result = self._detect_user(pose_keypoints)

        # 更新缓存
        self.user_cache[cache_key] = (result, timestamp)

        # 清理过期缓存
        self._cleanup_expired_cache(timestamp)

        return result
```

#### 数据库连接优化

```python
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

# 数据库连接池配置
engine = create_engine(
    'sqlite:///rehabilitation.db',
    poolclass=QueuePool,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600
)

class DatabaseManager:
    def __init__(self):
        self.engine = engine

    def get_connection(self):
        """获取数据库连接"""
        return self.engine.connect()

    def execute_with_retry(self, query, max_retries=3):
        """带重试的数据库执行"""
        for attempt in range(max_retries):
            try:
                with self.get_connection() as conn:
                    return conn.execute(query)
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                time.sleep(0.1 * (2 ** attempt))  # 指数退避
```

### 3. 网络通信优化

#### WebSocket连接优化

```python
class OptimizedWebSocketManager:
    def __init__(self):
        self.message_buffer = []
        self.buffer_size = 10
        self.last_flush_time = time.time()
        self.flush_interval = 0.033  # 30FPS

    def send_buffered_message(self, message_type, data):
        """缓冲消息发送，减少网络开销"""
        self.message_buffer.append({
            'type': message_type,
            'data': data,
            'timestamp': time.time()
        })

        # 达到缓冲区大小或时间间隔时刷新
        if (len(self.message_buffer) >= self.buffer_size or
            time.time() - self.last_flush_time >= self.flush_interval):
            self._flush_message_buffer()

    def _flush_message_buffer(self):
        """刷新消息缓冲区"""
        if not self.message_buffer:
            return

        # 合并相同类型的消息，只保留最新的
        merged_messages = {}
        for msg in self.message_buffer:
            merged_messages[msg['type']] = msg

        # 发送合并后的消息
        for msg in merged_messages.values():
            self.socketio.emit(msg['type'], msg['data'])

        self.message_buffer.clear()
        self.last_flush_time = time.time()
```

#### ZMQ通信优化

```python
class OptimizedZMQReceiver:
    def __init__(self):
        self.context = zmq.Context()
        self.context.setsockopt(zmq.IO_THREADS, 4)  # 增加IO线程

        # 优化socket配置
        self.detect_socket = self.context.socket(zmq.SUB)
        self.detect_socket.setsockopt(zmq.RCVHWM, 1000)  # 接收高水位标记
        self.detect_socket.setsockopt(zmq.RCVTIMEO, 100)  # 接收超时
        self.detect_socket.setsockopt(zmq.CONFLATE, 1)    # 只保留最新消息

    def receive_with_priority(self):
        """优先级接收消息"""
        messages = []

        # 首先接收所有可用消息
        while True:
            try:
                msg = self.detect_socket.recv_multipart(zmq.NOBLOCK)
                messages.append(msg)
            except zmq.Again:
                break

        # 只处理最新的消息，丢弃过时的
        if messages:
            return messages[-1]  # 返回最新消息

        return None
```

## 安全架构

### 1. 网络安全

#### HTTPS/WSS配置

```nginx
# Nginx SSL配置
server {
    listen 443 ssl http2;
    server_name rehabilitation.example.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # WebSocket升级支持
    location /socket.io/ {
        proxy_pass http://backend_servers;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

#### 数据传输加密

```python
class SecureDataTransmission:
    def __init__(self):
        self.encryption_key = self._generate_encryption_key()

    def encrypt_sensitive_data(self, data: Dict[str, Any]) -> str:
        """加密敏感数据"""
        from cryptography.fernet import Fernet

        f = Fernet(self.encryption_key)
        json_data = json.dumps(data)
        encrypted_data = f.encrypt(json_data.encode())
        return base64.b64encode(encrypted_data).decode()

    def decrypt_sensitive_data(self, encrypted_data: str) -> Dict[str, Any]:
        """解密敏感数据"""
        from cryptography.fernet import Fernet

        f = Fernet(self.encryption_key)
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted_data = f.decrypt(encrypted_bytes)
        return json.loads(decrypted_data.decode())
```

### 2. 身份认证和授权

#### 用户会话管理

```python
class SessionManager:
    def __init__(self):
        self.active_sessions = {}
        self.session_timeout = 3600  # 1小时

    def create_session(self, user_id: str, client_id: str) -> str:
        """创建用户会话"""
        session_id = str(uuid.uuid4())
        session_data = {
            'user_id': user_id,
            'client_id': client_id,
            'created_at': time.time(),
            'last_activity': time.time(),
            'permissions': self._get_user_permissions(user_id)
        }

        self.active_sessions[session_id] = session_data
        return session_id

    def validate_session(self, session_id: str) -> bool:
        """验证会话有效性"""
        if session_id not in self.active_sessions:
            return False

        session = self.active_sessions[session_id]
        current_time = time.time()

        # 检查会话是否过期
        if current_time - session['last_activity'] > self.session_timeout:
            del self.active_sessions[session_id]
            return False

        # 更新最后活动时间
        session['last_activity'] = current_time
        return True
```

#### 权限控制

```python
from functools import wraps

def require_permission(permission: str):
    """权限装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            session_id = request.headers.get('Session-ID')

            if not session_manager.validate_session(session_id):
                return {'error': '会话无效'}, 401

            session = session_manager.get_session(session_id)
            if permission not in session['permissions']:
                return {'error': '权限不足'}, 403

            return f(*args, **kwargs)
        return decorated_function
    return decorator

@require_permission('training_access')
def start_training_session():
    """需要训练权限的接口"""
    pass
```

### 3. 数据安全

#### 敏感数据处理

```python
class DataSanitizer:
    """数据清理和脱敏"""

    @staticmethod
    def sanitize_user_info(user_info: Dict[str, Any]) -> Dict[str, Any]:
        """用户信息脱敏"""
        sanitized = user_info.copy()

        # 脱敏个人信息
        if 'name' in sanitized:
            sanitized['name'] = DataSanitizer._mask_name(sanitized['name'])

        # 移除敏感字段
        sensitive_fields = ['phone', 'email', 'address']
        for field in sensitive_fields:
            sanitized.pop(field, None)

        return sanitized

    @staticmethod
    def _mask_name(name: str) -> str:
        """姓名脱敏"""
        if len(name) <= 2:
            return name
        return name[0] + '*' * (len(name) - 2) + name[-1]
```

## 监控和日志

### 1. 系统监控

#### 性能指标监控

```python
class SystemMonitor:
    def __init__(self):
        self.metrics = {
            'websocket_connections': 0,
            'messages_per_second': 0,
            'average_response_time': 0.0,
            'error_rate': 0.0,
            'cpu_usage': 0.0,
            'memory_usage': 0.0
        }
        self.start_time = time.time()

    def record_message(self, processing_time: float):
        """记录消息处理时间"""
        self.metrics['messages_per_second'] += 1

        # 更新平均响应时间
        current_avg = self.metrics['average_response_time']
        message_count = self.metrics['messages_per_second']
        self.metrics['average_response_time'] = (
            (current_avg * (message_count - 1) + processing_time) / message_count
        )

    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        import psutil

        return {
            'uptime': time.time() - self.start_time,
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'active_connections': self.metrics['websocket_connections'],
            'messages_per_second': self.metrics['messages_per_second'],
            'average_response_time': self.metrics['average_response_time'],
            'error_rate': self.metrics['error_rate']
        }
```

#### 健康检查端点

```python
@app.route('/health')
def health_check():
    """系统健康检查"""
    try:
        # 检查数据库连接
        db_status = check_database_connection()

        # 检查ZMQ连接
        zmq_status = check_zmq_connection()

        # 检查系统资源
        system_health = system_monitor.get_system_health()

        health_status = {
            'status': 'healthy',
            'timestamp': time.time(),
            'database': db_status,
            'zmq': zmq_status,
            'system': system_health
        }

        # 判断整体健康状态
        if not all([db_status['connected'], zmq_status['connected']]):
            health_status['status'] = 'unhealthy'
            return jsonify(health_status), 503

        return jsonify(health_status), 200

    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': time.time()
        }), 500
```

### 2. 日志系统

#### 结构化日志配置

```python
import logging
import json
from datetime import datetime

class StructuredLogger:
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # 文件处理器
        file_handler = logging.FileHandler('rehabilitation_system.log')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

    def log_event(self, event_type: str, data: Dict[str, Any], level: str = 'info'):
        """记录结构化事件"""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'data': data
        }

        log_message = json.dumps(log_entry, ensure_ascii=False)

        if level == 'error':
            self.logger.error(log_message)
        elif level == 'warning':
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
```

#### 关键事件日志

```python
class EventLogger:
    def __init__(self):
        self.logger = StructuredLogger('rehabilitation_events')

    def log_user_login(self, user_id: str, session_id: str):
        """记录用户登录事件"""
        self.logger.log_event('user_login', {
            'user_id': user_id,
            'session_id': session_id,
            'ip_address': request.remote_addr
        })

    def log_training_session(self, user_id: str, session_data: Dict[str, Any]):
        """记录训练会话事件"""
        self.logger.log_event('training_session', {
            'user_id': user_id,
            'session_duration': session_data.get('duration'),
            'actions_completed': session_data.get('actions_completed'),
            'average_score': session_data.get('average_score')
        })

    def log_system_error(self, error_type: str, error_details: Dict[str, Any]):
        """记录系统错误事件"""
        self.logger.log_event('system_error', {
            'error_type': error_type,
            'error_details': error_details,
            'stack_trace': traceback.format_exc()
        }, level='error')
```

## 故障排除

### 常见问题诊断

#### 1. WebSocket连接问题

```python
def diagnose_websocket_issues():
    """诊断WebSocket连接问题"""
    issues = []

    # 检查端口占用
    if not is_port_available(5000):
        issues.append("端口5000被占用")

    # 检查防火墙设置
    if not check_firewall_rules():
        issues.append("防火墙阻止WebSocket连接")

    # 检查SSL证书
    if not validate_ssl_certificate():
        issues.append("SSL证书无效或过期")

    # 检查代理配置
    if not check_proxy_configuration():
        issues.append("代理服务器配置错误")

    return issues
```

#### 2. 性能问题诊断

```python
def diagnose_performance_issues():
    """诊断性能问题"""
    import psutil

    issues = []

    # CPU使用率检查
    cpu_percent = psutil.cpu_percent(interval=1)
    if cpu_percent > 80:
        issues.append(f"CPU使用率过高: {cpu_percent}%")

    # 内存使用率检查
    memory = psutil.virtual_memory()
    if memory.percent > 85:
        issues.append(f"内存使用率过高: {memory.percent}%")

    # 磁盘空间检查
    disk = psutil.disk_usage('/')
    if disk.percent > 90:
        issues.append(f"磁盘空间不足: {disk.percent}%")

    # 网络连接检查
    connections = len(psutil.net_connections())
    if connections > 1000:
        issues.append(f"网络连接数过多: {connections}")

    return issues
```

### 故障恢复机制

#### 自动重启机制

```python
class AutoRecoveryManager:
    def __init__(self):
        self.max_failures = 3
        self.failure_count = 0
        self.last_failure_time = 0
        self.recovery_interval = 60  # 60秒

    def handle_failure(self, error: Exception):
        """处理系统故障"""
        current_time = time.time()

        # 重置故障计数（如果距离上次故障超过恢复间隔）
        if current_time - self.last_failure_time > self.recovery_interval:
            self.failure_count = 0

        self.failure_count += 1
        self.last_failure_time = current_time

        # 记录故障
        event_logger.log_system_error('system_failure', {
            'error': str(error),
            'failure_count': self.failure_count
        })

        # 执行恢复策略
        if self.failure_count <= self.max_failures:
            self._attempt_recovery()
        else:
            self._emergency_shutdown()

    def _attempt_recovery(self):
        """尝试系统恢复"""
        try:
            # 重置组件状态
            self._reset_components()

            # 重新初始化连接
            self._reinitialize_connections()

            # 验证系统状态
            if self._validate_system_health():
                self.failure_count = 0
                logging.info("系统恢复成功")
            else:
                raise Exception("系统恢复验证失败")

        except Exception as e:
            logging.error(f"系统恢复失败: {e}")
            raise

    def _emergency_shutdown(self):
        """紧急关闭系统"""
        logging.critical("系统故障次数超过限制，执行紧急关闭")

        # 保存关键数据
        self._save_critical_data()

        # 通知管理员
        self._notify_administrators()

        # 优雅关闭系统
        sys.exit(1)
```

---

*本文档基于智能康复系统v3.0实现，涵盖了系统的完整架构设计、技术实现、部署方案、性能优化、安全考虑和运维监控等方面。最后更新时间：2023-12-21*