"""
智能康复系统 - 第二阶段测试脚本
测试业务逻辑集成组件
"""
import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_state_handlers():
    """测试状态处理器"""
    print("测试状态处理器...")
    
    try:
        from services.business.state_handlers import state_handler_factory
        from models.system_states import SystemState
        
        # 测试工厂初始化
        factory = state_handler_factory
        handlers = factory.get_all_handlers()
        
        print(f"✓ 状态处理器工厂: {len(handlers)} 个处理器")
        
        # 测试每个状态处理器
        for state, handler in handlers.items():
            if handler:
                print(f"✓ {state.value} 处理器: {handler.__class__.__name__}")
            else:
                print(f"✗ {state.value} 处理器缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 状态处理器测试失败: {e}")
        return False

def test_system_coordinator():
    """测试系统协调器"""
    print("\n测试系统协调器...")
    
    try:
        from services.business.system_coordinator import system_coordinator
        from models.system_states import SystemState, StateTransitionEvent
        
        # 测试获取系统状态
        status = system_coordinator.get_system_status()
        print(f"✓ 系统状态: {status.get('current_state', 'unknown')}")
        
        # 测试强制状态转换
        success = system_coordinator.force_state_transition(
            StateTransitionEvent.USER_DETECTED,
            detected_patient_id="test_user"
        )
        
        if success:
            print("✓ 状态转换成功")
        else:
            print("✗ 状态转换失败")
        
        # 重置系统
        system_coordinator.reset_system()
        print("✓ 系统重置成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 系统协调器测试失败: {e}")
        return False

def test_task_loader():
    """测试任务加载器"""
    print("\n测试任务加载器...")
    
    try:
        from services.business.task.task_loader import task_loader
        
        # 测试加载用户任务
        success = task_loader.load_user_actions("P001")
        if success:
            print("✓ 用户任务加载成功")
            
            # 测试获取当前任务
            current_action = task_loader.get_current_action()
            if current_action:
                print(f"✓ 当前任务: {current_action.action_info.action_type}")
            else:
                print("✗ 获取当前任务失败")
                return False
            
            # 测试任务进度
            progress = task_loader.get_action_progress()
            print(f"✓ 任务进度: {progress}")
            
        else:
            print("✗ 用户任务加载失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 任务加载器测试失败: {e}")
        return False

def test_data_flow():
    """测试数据流"""
    print("\n测试数据流...")
    
    try:
        from services.business.system_coordinator import system_coordinator
        from models.data_models import ZMQDetectData
        import time
        
        # 创建模拟姿态数据
        test_pose_data = ZMQDetectData(
            timestamp=time.time(),
            patient_id="P001",
            pose_keypoints=[[0.0, 0.0, 0.5] for _ in range(133)],  # 133个关键点
            pose_bbox=[100, 100, 200, 200],
            face_bbox=[120, 120, 180, 180]
        )
        
        # 测试数据处理
        system_coordinator.handle_pose_data(test_pose_data)
        print("✓ 姿态数据处理成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据流测试失败: {e}")
        return False

def test_integration():
    """测试组件集成"""
    print("\n测试组件集成...")
    
    try:
        # 测试导入所有组件
        from services.business.system_coordinator import system_coordinator
        from services.business.state_handlers import state_handler_factory
        from services.business.task.task_loader import task_loader
        from services.business.state_manager import state_manager
        
        print("✓ 所有业务组件导入成功")
        
        # 测试组件间连接
        current_state = state_manager.get_current_state()
        handler = state_handler_factory.get_handler(current_state)
        
        if handler:
            print(f"✓ 状态处理器连接正常: {current_state.value}")
        else:
            print(f"✗ 状态处理器连接失败: {current_state.value}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 组件集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("智能康复系统 - 第二阶段业务逻辑集成测试")
    print("=" * 60)
    
    # 配置日志
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        test_state_handlers,
        test_system_coordinator,
        test_task_loader,
        test_data_flow,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"第二阶段测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 第二阶段业务逻辑集成测试全部通过！")
        print("\n✅ 已完成功能:")
        print("   - 状态处理器工厂和7个状态处理器")
        print("   - 系统协调器和数据流管道")
        print("   - 任务加载器集成")
        print("   - 组件间通信协调")
        print("\n🚀 系统已准备好进行完整的康复训练流程！")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
