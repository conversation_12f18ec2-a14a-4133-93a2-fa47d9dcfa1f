# 智能康复系统前端

基于 Vue 3 + Vite + TailwindCSS + Element Plus 构建的智能康复系统前端应用。

## 技术栈

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 下一代前端构建工具
- **Vue Router** - 官方路由管理器
- **Pinia** - Vue 状态管理库
- **Element Plus** - Vue 3 组件库
- **TailwindCSS** - 实用优先的 CSS 框架

## 项目结构

```
src/
├── components/          # 可复用组件
│   ├── common/         # 通用组件
│   ├── training/       # 训练相关组件
│   ├── visualization/  # 可视化组件
│   └── report/         # 报告组件
├── views/              # 页面组件
│   ├── LoginView.vue   # 登录页面
│   ├── TrainingView.vue # 训练页面
│   └── ReportView.vue  # 报告页面
├── services/           # 服务层
├── stores/             # 状态管理
├── utils/              # 工具函数
├── router/             # 路由配置
└── style.css           # 全局样式
```

## 开发指南

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 开发规范

- 每个文件代码不超过 350 行
- 使用 Vue 3 Composition API
- 页面高度固定 100vh
- 优先适配浏览器页面显示
- 组件命名使用 PascalCase
- 工具函数使用 camelCase

## 功能特性

- 🎯 实时用户检测和登录
- 🏃‍♂️ 康复训练指导和监控
- 📊 训练数据可视化
- 📈 训练报告生成
- 🔄 WebSocket 实时通信
- 🎨 响应式界面设计
