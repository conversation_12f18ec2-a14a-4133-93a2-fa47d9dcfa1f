<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-6">
    <!-- 主容器 -->
    <div class="w-full max-w-6xl mx-auto">
      <!-- 欢迎标题区域 -->
      <div class="text-center mb-8">
        <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-6">
          <el-icon class="text-3xl text-white">
            <User />
          </el-icon>
        </div>
        <h1 class="text-4xl font-bold text-gray-800 mb-4">
          欢迎回来，{{ userDisplayName }}！
        </h1>
        <p class="text-xl text-gray-600 mb-6">
          今日为您准备了 {{ actionList.length }} 个训练任务，让我们开始您的康复之旅
        </p>
        
        <!-- 倒计时显示 -->
        <div class="inline-flex items-center bg-white/80 backdrop-blur-lg rounded-2xl px-8 py-4 shadow-lg border border-white/20">
          <el-icon class="text-2xl text-blue-500 mr-3">
            <Clock />
          </el-icon>
          <div class="text-center">
            <div class="text-sm text-gray-500 mb-1">任务介绍剩余时间</div>
            <div class="text-3xl font-bold text-blue-600">
              {{ formatTime(remainingTime) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 任务列表展示区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div
          v-for="(action, index) in actionList"
          :key="index"
          :class="[
            'bg-white/90 backdrop-blur-lg rounded-2xl p-6 shadow-xl border border-white/20 transition-all duration-500',
            index === currentTaskIndex ? 'ring-4 ring-blue-500 scale-105' : 'hover:scale-102'
          ]"
        >
          <!-- 任务编号 -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <div :class="[
                'w-10 h-10 rounded-full flex items-center justify-center text-white font-bold text-lg',
                index === currentTaskIndex ? 'bg-gradient-to-br from-blue-500 to-purple-600' : 'bg-gray-400'
              ]">
                {{ index + 1 }}
              </div>
              <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-800">
                  {{ getActionDisplayName(action) }}
                </h3>
                <p class="text-sm text-gray-500">
                  {{ action.side ? `${action.side}侧` : '' }}
                </p>
              </div>
            </div>
            
            <!-- 状态指示器 -->
            <div v-if="index === currentTaskIndex" class="flex items-center">
              <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse mr-2"></div>
              <span class="text-sm text-blue-600 font-medium">正在介绍</span>
            </div>
          </div>

          <!-- 任务描述 -->
          <div class="space-y-3">
            <div class="bg-gray-50 rounded-lg p-3">
              <h4 class="text-sm font-medium text-gray-700 mb-2">动作要点</h4>
              <p class="text-sm text-gray-600">
                {{ getActionDescription(action) }}
              </p>
            </div>
            
            <div class="bg-blue-50 rounded-lg p-3">
              <h4 class="text-sm font-medium text-blue-700 mb-2">训练目标</h4>
              <p class="text-sm text-blue-600">
                {{ getActionGoal(action) }}
              </p>
            </div>
          </div>

          <!-- 进度指示 -->
          <div class="mt-4">
            <div class="flex justify-between text-xs text-gray-500 mb-1">
              <span>准备程度</span>
              <span>{{ index <= currentTaskIndex ? '100%' : '0%' }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div 
                :class="[
                  'h-2 rounded-full transition-all duration-1000',
                  index <= currentTaskIndex ? 'bg-gradient-to-r from-blue-500 to-purple-600' : 'bg-gray-200'
                ]"
                :style="{ width: index <= currentTaskIndex ? '100%' : '0%' }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部提示信息 -->
      <div class="text-center">
        <div class="bg-white/80 backdrop-blur-lg rounded-2xl p-6 shadow-lg border border-white/20 max-w-2xl mx-auto">
          <div class="flex items-center justify-center mb-4">
            <el-icon class="text-2xl text-green-500 mr-3">
              <InfoFilled />
            </el-icon>
            <h3 class="text-lg font-semibold text-gray-800">准备提示</h3>
          </div>
          <p class="text-gray-600 mb-4">
            请仔细观看每个动作的介绍，了解动作要点和注意事项。倒计时结束后，系统将自动进入训练模式。
          </p>
          <div class="flex items-center justify-center space-x-6 text-sm text-gray-500">
            <div class="flex items-center">
              <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
              <span>当前介绍</span>
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span>已介绍</span>
            </div>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-gray-300 rounded-full mr-2"></div>
              <span>待介绍</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 响应式消息显示区域 -->
      <transition
        enter-active-class="transition-all duration-300 ease-out"
        leave-active-class="transition-all duration-200 ease-in"
        enter-from-class="opacity-0 transform translate-y-2"
        enter-to-class="opacity-100 transform translate-y-0"
        leave-from-class="opacity-100 transform translate-y-0"
        leave-to-class="opacity-0 transform translate-y-2"
      >
        <div v-if="currentNotification" class="fixed bottom-6 right-6 z-50">
          <div :class="[
            'p-4 rounded-xl border backdrop-blur-lg shadow-lg max-w-sm',
            notificationTypeClass
          ]">
            <div class="flex items-center">
              <el-icon :class="notificationIconClass" class="mr-3">
                <CircleCheckFilled v-if="currentNotification.type === 'success'" />
                <WarningFilled v-else-if="currentNotification.type === 'warning'" />
                <Loading v-else-if="currentNotification.type === 'info'" />
                <WarningFilled v-else />
              </el-icon>
              <div class="flex-1">
                <p :class="notificationTextClass" class="font-medium">
                  {{ currentNotification.content }}
                </p>
                <p class="text-xs opacity-75 mt-1">
                  {{ formatNotificationTime(currentNotification.timestamp) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useMainStore } from '@/stores/main'
import { User, Clock, InfoFilled, CircleCheckFilled, WarningFilled, Loading } from '@element-plus/icons-vue'
import { sendWebSocketMessage } from '@/services/websocket'

// 使用主store
const mainStore = useMainStore()

// 响应式数据
const currentTaskIndex = ref(0)
const taskIntroTimer = ref(null)
const countdownTimer = ref(null)
const remainingTime = ref(15) // 30秒倒计时

// 计算属性
const userInfo = computed(() => mainStore.userInfo)
const actionList = computed(() => mainStore.actionList || [])
const currentNotification = computed(() => mainStore.currentNotification)

// 用户显示名称
const userDisplayName = computed(() => {
  if (userInfo.value?.name) {
    return userInfo.value.name
  } else if (userInfo.value?.patient_id) {
    return `用户 ${userInfo.value.patient_id}`
  }
  return '用户'
})

// 消息通知样式计算属性
const notificationTypeClass = computed(() => {
  if (!currentNotification.value) return ''
  
  const baseClass = 'bg-white/90 border-white/30'
  switch (currentNotification.value.type) {
    case 'success':
      return `${baseClass} border-green-200 bg-green-50/90`
    case 'warning':
      return `${baseClass} border-yellow-200 bg-yellow-50/90`
    case 'error':
      return `${baseClass} border-red-200 bg-red-50/90`
    case 'info':
    default:
      return `${baseClass} border-blue-200 bg-blue-50/90`
  }
})

const notificationIconClass = computed(() => {
  if (!currentNotification.value) return ''
  
  switch (currentNotification.value.type) {
    case 'success':
      return 'text-green-500'
    case 'warning':
      return 'text-yellow-500'
    case 'error':
      return 'text-red-500'
    case 'info':
    default:
      return 'text-blue-500'
  }
})

const notificationTextClass = computed(() => {
  if (!currentNotification.value) return ''
  
  switch (currentNotification.value.type) {
    case 'success':
      return 'text-green-800'
    case 'warning':
      return 'text-yellow-800'
    case 'error':
      return 'text-red-800'
    case 'info':
    default:
      return 'text-blue-800'
  }
})

// 方法
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const formatNotificationTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diff = now - time
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else {
    return time.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }
}

const getActionDisplayName = (action) => {
  const typeMap = {
    'arm_raise': '手臂举起',
    'shoulder_touch': '肩部触摸',
    'leg_lift': '腿部抬起',
    'squat': '深蹲练习',
    'stretch': '拉伸运动'
  }
  return typeMap[action.action_type] || action.action_type
}

const getActionDescription = (action) => {
  const descMap = {
    'arm_raise': '缓慢将手臂向上举起，保持身体平衡，注意动作的连贯性',
    'shoulder_touch': '用手轻触对侧肩膀，增强肩部灵活性和协调性',
    'leg_lift': '抬起腿部至舒适高度，锻炼腿部肌肉力量和平衡能力',
    'squat': '双脚分开与肩同宽，缓慢下蹲，增强下肢力量',
    'stretch': '进行全身拉伸，放松肌肉，提高身体柔韧性'
  }
  return descMap[action.action_type] || '请按照指导完成相应动作'
}

const getActionGoal = (action) => {
  const goalMap = {
    'arm_raise': '提高上肢力量和关节活动度',
    'shoulder_touch': '增强肩部灵活性和手眼协调',
    'leg_lift': '强化下肢肌肉和平衡控制',
    'squat': '增强腿部力量和核心稳定性',
    'stretch': '改善身体柔韧性和血液循环'
  }
  return goalMap[action.action_type] || '促进身体机能恢复'
}

// 开始任务介绍动画
const startTaskIntroduction = () => {
  if (!actionList.value || actionList.value.length === 0) {
    console.log('没有任务列表，跳过介绍')
    return
  }
  
  console.log('开始任务介绍动画')
  currentTaskIndex.value = 0
  
  // 自动滑动展示任务
  const showNextTask = () => {
    if (currentTaskIndex.value < actionList.value.length - 1) {
      currentTaskIndex.value++
      taskIntroTimer.value = setTimeout(showNextTask, 3000) // 每3秒切换一个任务
    } else {
      // 所有任务展示完毕，重新开始循环
      currentTaskIndex.value = 0
      taskIntroTimer.value = setTimeout(showNextTask, 3000)
    }
  }
  // 开始第一个任务的展示
  taskIntroTimer.value = setTimeout(showNextTask, 3000)
}

// 开始倒计时
const startCountdown = () => {
  countdownTimer.value = setInterval(() => {
    remainingTime.value--
    if (remainingTime.value <= 0) {
      clearInterval(countdownTimer.value)
      // 倒计时结束，发送事件给后端
      handleCountdownFinish()
    }
  }, 1000)
}

// 处理倒计时结束
const handleCountdownFinish = () => {
  console.log('任务介绍倒计时结束，发送clock_introduction_finish事件')

  try {
    // 发送事件给后端
    sendWebSocketMessage('clock_introduction_finish', {
      message: '任务介绍倒计时完成',
      timestamp: Date.now()
    })

    console.log('clock_introduction_finish事件已发送')
  } catch (error) {
    console.error('发送clock_introduction_finish事件失败:', error)
  }
}

// 生命周期钩子
onMounted(() => {
  // 启动任务介绍动画
  startTaskIntroduction()
  
  // 启动倒计时
  startCountdown()
})

onUnmounted(() => {
  // 清理定时器
  if (taskIntroTimer.value) {
    clearTimeout(taskIntroTimer.value)
  }
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style scoped>
.hover\:scale-102:hover {
  transform: scale(1.02);
}
</style>
