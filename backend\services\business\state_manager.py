"""
智能康复系统 - 状态机管理器
控制系统状态转换和业务流程
"""
import logging
import time
import threading
from typing import Optional, Dict, Any, Callable
from models.system_states import (
    SystemState, StateTransitionEvent, StateTransitionRules,
    StateValidator, StateContext
)

class StateManager:
    """状态机管理器"""
    
    def __init__(self):
        """初始化状态管理器"""
        self.logger = logging.getLogger(__name__)
        
        # 当前状态上下文
        self.current_context = StateContext(
            current_state=SystemState.WAITING,
            state_start_time=time.time()
        )
        
        # 状态变化回调函数
        self.state_change_callbacks: Dict[SystemState, list] = {}
        
        # 状态转换锁
        self.transition_lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            'total_transitions': 0,
            'successful_transitions': 0,
            'failed_transitions': 0,
            'current_state_duration': 0.0
        }
        
        self.logger.info("状态机管理器初始化完成")
    
    def get_current_state(self) -> SystemState:
        """获取当前状态"""
        return self.current_context.current_state
    
    def get_current_context(self) -> StateContext:
        """获取当前状态上下文"""
        # 更新状态持续时间
        self.current_context.state_data = self.current_context.state_data or {}
        self.current_context.state_data['duration'] = time.time() - self.current_context.state_start_time
        return self.current_context
    
    def transition_to(self, event: StateTransitionEvent, **kwargs) -> bool:
        """
        执行状态转换
        
        Args:
            event: 转换事件
            **kwargs: 额外的状态数据
            
        Returns:
            bool: 转换是否成功
        """
        with self.transition_lock:
            try:
                self.stats['total_transitions'] += 1
                
                # 验证转换事件
                if not StateValidator.validate_transition_event(event, self.current_context):
                    self.logger.warning(f"无效的状态转换事件: {event.value}")
                    self.stats['failed_transitions'] += 1
                    return False
                
                # 获取转换规则
                transition = StateTransitionRules.get_transition(
                    self.current_context.current_state, 
                    event
                )
                
                if not transition:
                    self.logger.warning(
                        f"未找到转换规则: {self.current_context.current_state.value} -> {event.value}"
                    )
                    self.stats['failed_transitions'] += 1
                    return False
                
                # 执行状态转换
                old_state = self.current_context.current_state
                new_state = transition.to_state
                
                # 更新状态上下文
                self.current_context.previous_state = old_state
                self.current_context.current_state = new_state
                self.current_context.state_start_time = time.time()
                
                # 更新状态数据
                if kwargs:
                    self.current_context.state_data = self.current_context.state_data or {}
                    self.current_context.state_data.update(kwargs)
                
                self.stats['successful_transitions'] += 1
                
                self.logger.info(
                    f"状态转换成功: {old_state.value} -> {new_state.value} (事件: {event.value})"
                )
                
                # 触发状态变化回调
                self._trigger_state_callbacks(new_state, old_state)

                return True
                
            except Exception as e:
                self.logger.error(f"状态转换失败: {e}")
                self.stats['failed_transitions'] += 1
                return False
    
    def register_state_callback(self, state: SystemState, callback: Callable):
        """
        注册状态变化回调函数
        
        Args:
            state: 目标状态
            callback: 回调函数
        """
        if state not in self.state_change_callbacks:
            self.state_change_callbacks[state] = []
        
        self.state_change_callbacks[state].append(callback)
        self.logger.debug(f"注册状态回调: {state.value}")
    
    def _trigger_state_callbacks(self, new_state: SystemState, old_state: SystemState):
        """触发状态变化回调"""
        try:
            if new_state in self.state_change_callbacks:
                for callback in self.state_change_callbacks[new_state]:
                    try:
                        callback(new_state, old_state, self.current_context)
                    except Exception as e:
                        self.logger.error(f"状态回调执行失败: {e}")
        
        except Exception as e:
            self.logger.error(f"触发状态回调失败: {e}")

    def reset_to_waiting(self):
        """重置状态机到WAITING状态"""
        try:
            with self.transition_lock:
                old_state = self.current_context.current_state

                # 重置到WAITING状态
                self.current_context.previous_state = old_state
                self.current_context.current_state = SystemState.WAITING
                self.current_context.state_start_time = time.time()
                self.current_context.state_data = {}

                # 触发回调
                self._trigger_state_callbacks(SystemState.WAITING, old_state)

                self.logger.info(f"状态重置完成: {old_state.value} -> WAITING")

        except Exception as e:
            self.logger.error(f"状态重置到WAITING失败: {e}")

    def get_valid_transitions(self) -> list:
        """获取当前状态的有效转换"""
        return StateTransitionRules.get_valid_transitions_from_state(
            self.current_context.current_state
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        current_duration = time.time() - self.current_context.state_start_time
        
        return {
            **self.stats,
            'current_state': self.current_context.current_state.value,
            'current_state_duration': current_duration,
            'previous_state': self.current_context.previous_state.value if self.current_context.previous_state else None,
            'valid_transitions': [t.event.value for t in self.get_valid_transitions()]
        }
    
    def is_in_state(self, state: SystemState) -> bool:
        """检查是否处于指定状态"""
        return self.current_context.current_state == state
    
    def get_state_duration(self) -> float:
        """获取当前状态持续时间"""
        return time.time() - self.current_context.state_start_time

# 全局状态管理器实例
state_manager = StateManager()
