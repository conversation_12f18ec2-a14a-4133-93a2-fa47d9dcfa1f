/**
 * 性能优化工具
 * 提供关键点渲染和前端应用的性能优化功能
 */

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait, immediate = false) {
  let timeout
  
  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }
    
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func.apply(this, args)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
  let inThrottle
  
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 关键点数据防抖处理器
 * 专门用于处理高频的关键点数据更新
 */
export class KeypointDataThrottler {
  constructor(callback, interval = 16) { // 默认60FPS
    this.callback = callback
    this.interval = interval
    this.lastCallTime = 0
    this.pendingData = null
    this.animationId = null
  }

  /**
   * 更新关键点数据
   * @param {Array} keypoints - 关键点数据
   * @param {Array} requiredKeypoints - 需要的关键点索引
   */
  update(keypoints, requiredKeypoints) {
    this.pendingData = { keypoints, requiredKeypoints }
    
    if (!this.animationId) {
      this.animationId = requestAnimationFrame(() => this.processUpdate())
    }
  }

  /**
   * 处理更新
   */
  processUpdate() {
    const now = Date.now()
    
    if (now - this.lastCallTime >= this.interval && this.pendingData) {
      this.callback(this.pendingData.keypoints, this.pendingData.requiredKeypoints)
      this.lastCallTime = now
      this.pendingData = null
    }
    
    this.animationId = null
    
    // 如果还有待处理的数据，继续下一帧
    if (this.pendingData) {
      this.animationId = requestAnimationFrame(() => this.processUpdate())
    }
  }

  /**
   * 销毁节流器
   */
  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
    this.pendingData = null
  }
}

/**
 * 性能监控器
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = {
      fps: 0,
      frameTime: 0,
      renderTime: 0,
      memoryUsage: 0,
      keypointCount: 0
    }
    
    this.frameCount = 0
    this.lastTime = performance.now()
    this.frameTimes = []
    this.maxFrameHistory = 60
    
    this.isMonitoring = false
    this.monitoringId = null
  }

  /**
   * 开始监控
   */
  start() {
    if (this.isMonitoring) return
    
    this.isMonitoring = true
    this.lastTime = performance.now()
    this.frameCount = 0
    this.frameTimes = []
    
    this.monitoringId = requestAnimationFrame(() => this.update())
  }

  /**
   * 停止监控
   */
  stop() {
    this.isMonitoring = false
    
    if (this.monitoringId) {
      cancelAnimationFrame(this.monitoringId)
      this.monitoringId = null
    }
  }

  /**
   * 更新性能指标
   */
  update() {
    if (!this.isMonitoring) return
    
    const now = performance.now()
    const deltaTime = now - this.lastTime
    
    this.frameCount++
    this.frameTimes.push(deltaTime)
    
    // 保持帧时间历史在合理范围内
    if (this.frameTimes.length > this.maxFrameHistory) {
      this.frameTimes.shift()
    }
    
    // 每秒更新一次FPS
    if (this.frameCount >= 60) {
      this.calculateMetrics()
      this.frameCount = 0
    }
    
    this.lastTime = now
    this.monitoringId = requestAnimationFrame(() => this.update())
  }

  /**
   * 计算性能指标
   */
  calculateMetrics() {
    if (this.frameTimes.length === 0) return
    
    // 计算平均帧时间
    const avgFrameTime = this.frameTimes.reduce((sum, time) => sum + time, 0) / this.frameTimes.length
    
    // 计算FPS
    this.metrics.fps = Math.round(1000 / avgFrameTime)
    this.metrics.frameTime = Math.round(avgFrameTime * 100) / 100
    
    // 获取内存使用情况（如果支持）
    if (performance.memory) {
      this.metrics.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024)
    }
  }

  /**
   * 记录渲染时间
   * @param {number} renderTime - 渲染时间（毫秒）
   */
  recordRenderTime(renderTime) {
    this.metrics.renderTime = Math.round(renderTime * 100) / 100
  }

  /**
   * 记录关键点数量
   * @param {number} count - 关键点数量
   */
  recordKeypointCount(count) {
    this.metrics.keypointCount = count
  }

  /**
   * 获取性能指标
   * @returns {Object} 性能指标对象
   */
  getMetrics() {
    return { ...this.metrics }
  }

  /**
   * 获取性能等级
   * @returns {string} 性能等级 (excellent, good, fair, poor)
   */
  getPerformanceGrade() {
    const fps = this.metrics.fps
    
    if (fps >= 55) return 'excellent'
    if (fps >= 45) return 'good'
    if (fps >= 30) return 'fair'
    return 'poor'
  }
}

/**
 * Canvas优化工具
 */
export class CanvasOptimizer {
  constructor(canvas) {
    this.canvas = canvas
    this.ctx = canvas.getContext('2d')
    this.imageDataCache = new Map()
    this.pathCache = new Map()
  }

  /**
   * 优化Canvas设置
   */
  optimizeCanvas() {
    // 设置图像平滑
    this.ctx.imageSmoothingEnabled = true
    this.ctx.imageSmoothingQuality = 'high'
    
    // 设置线条样式
    this.ctx.lineCap = 'round'
    this.ctx.lineJoin = 'round'
    
    // 设置文本渲染
    this.ctx.textBaseline = 'middle'
    this.ctx.textAlign = 'center'
  }

  /**
   * 批量绘制点
   * @param {Array} points - 点数组 [[x, y, color], ...]
   * @param {number} radius - 点半径
   */
  batchDrawPoints(points, radius = 4) {
    // 按颜色分组
    const colorGroups = new Map()
    
    points.forEach(([x, y, color]) => {
      if (!colorGroups.has(color)) {
        colorGroups.set(color, [])
      }
      colorGroups.get(color).push([x, y])
    })
    
    // 批量绘制同色点
    colorGroups.forEach((pointList, color) => {
      this.ctx.fillStyle = color
      this.ctx.beginPath()
      
      pointList.forEach(([x, y]) => {
        this.ctx.moveTo(x + radius, y)
        this.ctx.arc(x, y, radius, 0, Math.PI * 2)
      })
      
      this.ctx.fill()
    })
  }

  /**
   * 批量绘制线条
   * @param {Array} lines - 线条数组 [[x1, y1, x2, y2, color], ...]
   * @param {number} lineWidth - 线条宽度
   */
  batchDrawLines(lines, lineWidth = 2) {
    // 按颜色分组
    const colorGroups = new Map()
    
    lines.forEach(([x1, y1, x2, y2, color]) => {
      if (!colorGroups.has(color)) {
        colorGroups.set(color, [])
      }
      colorGroups.get(color).push([x1, y1, x2, y2])
    })
    
    // 批量绘制同色线条
    this.ctx.lineWidth = lineWidth
    
    colorGroups.forEach((lineList, color) => {
      this.ctx.strokeStyle = color
      this.ctx.beginPath()
      
      lineList.forEach(([x1, y1, x2, y2]) => {
        this.ctx.moveTo(x1, y1)
        this.ctx.lineTo(x2, y2)
      })
      
      this.ctx.stroke()
    })
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.imageDataCache.clear()
    this.pathCache.clear()
  }
}

/**
 * 内存管理工具
 */
export class MemoryManager {
  constructor() {
    this.objectPools = new Map()
    this.cleanupCallbacks = []
  }

  /**
   * 创建对象池
   * @param {string} type - 对象类型
   * @param {Function} factory - 对象工厂函数
   * @param {number} initialSize - 初始大小
   */
  createObjectPool(type, factory, initialSize = 10) {
    const pool = []
    
    for (let i = 0; i < initialSize; i++) {
      pool.push(factory())
    }
    
    this.objectPools.set(type, {
      pool,
      factory,
      inUse: new Set()
    })
  }

  /**
   * 获取对象
   * @param {string} type - 对象类型
   * @returns {*} 对象实例
   */
  getObject(type) {
    const poolData = this.objectPools.get(type)
    if (!poolData) return null
    
    let obj = poolData.pool.pop()
    if (!obj) {
      obj = poolData.factory()
    }
    
    poolData.inUse.add(obj)
    return obj
  }

  /**
   * 归还对象
   * @param {string} type - 对象类型
   * @param {*} obj - 对象实例
   */
  returnObject(type, obj) {
    const poolData = this.objectPools.get(type)
    if (!poolData || !poolData.inUse.has(obj)) return
    
    poolData.inUse.delete(obj)
    poolData.pool.push(obj)
  }

  /**
   * 添加清理回调
   * @param {Function} callback - 清理回调函数
   */
  addCleanupCallback(callback) {
    this.cleanupCallbacks.push(callback)
  }

  /**
   * 执行内存清理
   */
  cleanup() {
    // 执行清理回调
    this.cleanupCallbacks.forEach(callback => {
      try {
        callback()
      } catch (error) {
        console.warn('清理回调执行失败:', error)
      }
    })
    
    // 清理对象池
    this.objectPools.forEach(poolData => {
      poolData.pool.length = 0
      poolData.inUse.clear()
    })
    
    // 强制垃圾回收（如果支持）
    if (window.gc) {
      window.gc()
    }
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()
export const memoryManager = new MemoryManager()

// 开发环境下暴露到全局
if (import.meta.env.DEV) {
  window.performanceMonitor = performanceMonitor
  window.memoryManager = memoryManager
}
