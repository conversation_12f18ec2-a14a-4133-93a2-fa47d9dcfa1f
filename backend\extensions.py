"""
智能康复系统 - 扩展管理
统一管理Flask扩展实例，确保上下文一致性
"""
from flask_socketio import SocketIO
from flask_cors import CORS

# 创建扩展实例（延迟初始化）
socketio = SocketIO()
cors = CORS()

def init_extensions(app):
    """初始化所有扩展"""
    # 初始化SocketIO - 使用threading模式确保跨线程兼容性
    socketio.init_app(
        app,
        cors_allowed_origins="*",
        async_mode='threading',  # 改为threading模式
        ping_timeout=60,
        ping_interval=25,
        logger=True,
        engineio_logger=False
    )
    
    # 初始化CORS
    cors.init_app(app, origins="*")
    
    return app