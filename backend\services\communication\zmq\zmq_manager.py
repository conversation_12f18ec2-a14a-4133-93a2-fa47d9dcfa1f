"""
智能康复系统 - ZMQ连接管理器
管理ZMQ连接的生命周期和状态监控
"""
import time
import logging
import threading
from typing import Dict, Any, Optional, Callable
from .zmq_receiver import ZMQReceiver
from models.data_models import ZMQDetectData, ZMQCameraFrame
from models.constants import SystemConfig

class ZMQConnectionManager:
    """ZMQ连接管理器"""
    
    def __init__(self):
        """初始化连接管理器"""
        self.receiver = ZMQReceiver()
        self.logger = logging.getLogger(__name__)
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 连接状态回调
        self.connection_status_callback: Optional[Callable[[Dict[str, Any]], None]] = None
        
        # 数据处理回调
        self.data_handlers = {
            'detect_data': [],
            'camera_frame': []
        }
        
        # 重连配置
        self.auto_reconnect = True
        self.reconnect_interval = 5.0  # 秒
        self.max_reconnect_attempts = 10
        self.reconnect_attempts = 0
    
    def add_data_handler(self, data_type: str, handler: Callable):
        """
        添加数据处理器
        
        Args:
            data_type: 数据类型 ('detect_data' 或 'camera_frame')
            handler: 处理函数
        """
        if data_type in self.data_handlers:
            self.data_handlers[data_type].append(handler)
            self.logger.info(f"添加{data_type}处理器: {handler.__name__}")
    
    def remove_data_handler(self, data_type: str, handler: Callable):
        """
        移除数据处理器
        
        Args:
            data_type: 数据类型
            handler: 处理函数
        """
        if data_type in self.data_handlers and handler in self.data_handlers[data_type]:
            self.data_handlers[data_type].remove(handler)
            self.logger.info(f"移除{data_type}处理器: {handler.__name__}")
    
    def set_connection_status_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        设置连接状态回调
        
        Args:
            callback: 状态变化回调函数
        """
        self.connection_status_callback = callback
    
    def start(self) -> bool:
        """
        启动ZMQ连接管理器
        
        Returns:
            bool: 启动是否成功
        """
        try:
            # 启动接收器
            if not self.receiver.start_receiving():
                self.logger.error("启动ZMQ接收器失败")
                return False
            # 启动监控线程
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_connections, daemon=True)
            self.monitor_thread.start()
            
            self.logger.info("ZMQ连接管理器启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"启动ZMQ连接管理器失败: {e}")
            return False
    
    def stop(self):
        """停止ZMQ连接管理器"""
        try:
            # 停止监控
            self.is_monitoring = False
            
            # 停止接收器
            self.receiver.stop_receiving()
            
            # 等待监控线程结束
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=2.0)
            
            self.logger.info("ZMQ连接管理器已停止")
            
        except Exception as e:
            self.logger.error(f"停止ZMQ连接管理器失败: {e}")
    
    def _handle_detect_data(self, detect_data: ZMQDetectData):
        """
        处理检测数据
        
        Args:
            detect_data: 检测数据
        """
        try:
            # 重置重连计数
            self.reconnect_attempts = 0
            
            # 调用所有处理器
            for handler in self.data_handlers['detect_data']:
                try:
                    handler(detect_data)
                except Exception as e:
                    self.logger.error(f"检测数据处理器错误 {handler.__name__}: {e}")
                    
        except Exception as e:
            self.logger.error(f"处理检测数据失败: {e}")
    
    def _handle_camera_frame(self, camera_frame: ZMQCameraFrame):
        """
        处理摄像头帧数据

        Args:
            camera_frame: 摄像头帧数据
        """
        try:
            # 重置重连计数
            self.reconnect_attempts = 0

            # 调用所有处理器
            for handler in self.data_handlers['camera_frame']:
                try:
                    handler(camera_frame)
                    # self.logger.info(f"✅ 处理器 {handler.__name__} 执行完成")
                except Exception as e:
                    self.logger.error(f"❌ 摄像头帧处理器错误 {handler.__name__}: {e}")

        except Exception as e:
            self.logger.error(f"❌ 处理摄像头帧失败: {e}")
    
    def _monitor_connections(self):
        """监控连接状态"""
        last_status = None
        while self.is_monitoring:
            try:
                # 获取当前状态
                current_status = self.receiver.get_connection_status()
                # 检查状态变化
                if current_status != last_status:
                    self.logger.info(f"连接状态变化: {current_status}")
                    # 调用状态回调
                    if self.connection_status_callback:
                        try:
                            self.connection_status_callback(current_status)
                        except Exception as e:
                            self.logger.error(f"连接状态回调错误: {e}")
                    
                    last_status = current_status.copy()
                time.sleep(5.0)  # 每5秒检查一次
                
            except Exception as e:
                self.logger.error(f"连接监控错误: {e}")
                time.sleep(1.0)

    def cleanup(self):
        """清理资源"""
        self.logger.info("🛑 清理ZMQ连接管理器资源...")
        # 停止监控
        self.stop_monitoring()
        # 断开连接
        self.disconnect()
        # 清理接收器
        if self.receiver:
            self.receiver.cleanup()
        self.logger.info("✅ ZMQ连接管理器资源已清理")

# 全局ZMQ管理器实例
zmq_manager = ZMQConnectionManager()
