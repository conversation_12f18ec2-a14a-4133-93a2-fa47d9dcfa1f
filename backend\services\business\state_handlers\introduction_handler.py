"""
智能康复系统 - INTRODUCTION状态处理器
处理用户登录成功后的欢迎和任务介绍阶段
"""
import time
from typing import Dict, Any
from models.system_states import SystemState, StateTransitionEvent, MessageType, TimeConstants
from models.data_models import ZMQDetectData, SystemStateData
from . import BaseStateHandler
from ..user_detection_service import user_detection_service

class IntroductionHandler(BaseStateHandler):
    """INTRODUCTION状态处理器"""
    
    def __init__(self):
        """初始化INTRODUCTION状态处理器"""
        super().__init__(SystemState.INTRODUCTION)
        self.introduction_start_time = None
        self.user_lost_start_time = None
        self.last_user_id = None
        self.last_check_time = 0  # 上次检查时间
        self.check_interval = 1.0  # 检查间隔（秒）
    
    def enter_state(self, context: Dict[str, Any]):
        """进入INTRODUCTION状态"""
        self.logger.info("系统进入欢迎和任务介绍状态")
        self.introduction_start_time = time.time()
        
    
    def handle_data(self, data: Any, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理INTRODUCTION状态下的数据"""
        try:
            user_status = self._check_user_status(data, context)
            # 检查是否需要暂停
            if user_status["should_pause"]:
                return self._handle_pause(user_status, context)
            # 不再检查介绍时间，由前端控制倒计时
            return {
                "success": True,
            }

        except Exception as e:
            self.logger.error(f"处理介绍状态数据失败: {e}")
            # 错误情况下也要发送状态数据
            error_state_data = SystemStateData(
                current_state=SystemState.INTRODUCTION,
                message=f"数据处理失败: {str(e)}",
                user_info=context.get("user_info"),
                action_list=context.get("action_list", []),
                pose_keypoints=[],
            )
            return {
                "success": False,
                "state_data": error_state_data,
                "message": f"数据处理失败: {str(e)}"
            }
    
    def _check_user_status(self, pose_data: ZMQDetectData, context: Dict[str, Any]) -> Dict[str, Any]:
        """检查用户状态 - 使用公共用户检测服务"""
        # 使用公共用户检测服务检查用户在场情况
        presence_result = user_detection_service.check_user(pose_data, context)
        
        if presence_result.get("should_pause"):
            return {
                "should_pause": True,
                "pause_reason": presence_result.get("pause_reason"),
                "message": presence_result.get("message")
            }

        return {"should_pause": False}
    
    def _handle_pause(self, user_status: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理暂停逻辑"""
        pause_reason = user_status["pause_reason"]

        if pause_reason == "user_lost":
            event = StateTransitionEvent.USER_LOST
            message_type = MessageType.USER_LOST
        else:  # user_not_auth
            event = StateTransitionEvent.USER_NOT_AUTH
            message_type = MessageType.USER_NOT_AUTH

        # 创建暂停状态数据
        state_data = SystemStateData(
            current_state=SystemState.PAUSE,
            message=user_status["message"],
            user_info=context.get("user_info"),
            action_list=context.get("action_list", []),
            pose_keypoints=[],  # 暂停时不发送姿态数据
        )

        return {
            "success": True,
            "trigger_event": event,
            "next_state": SystemState.PAUSE,
            "websocket_message": message_type,
            "state_data": state_data,
            "message": user_status["message"]
        }
    

    def handle_introduction_finish(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理前端发送的介绍完成事件"""
        try:
            self.logger.info("收到前端介绍完成事件，准备转换到PREPARATION状态")

            # 获取用户信息和动作列表
            user_info = context.get("user_info")
            action_list = context.get("action_list", [])

            # 创建状态数据
            state_data = SystemStateData(
                current_state=SystemState.PREPARATION,
                message="任务介绍完成，开始准备训练",
                user_info=user_info,
                action_list=action_list,
                pose_keypoints=[],
            )
            return {
                "success": True,
                "trigger_event": StateTransitionEvent.CLOCK_INTRODUCTION_FINISH,
                "next_state": SystemState.PREPARATION,
                "websocket_message": MessageType.CLOCK_INTRODUCTION_FINISH,
                "state_data": state_data
            }

        except Exception as e:
            self.logger.error(f"处理介绍完成事件失败: {e}")
            return {
                "success": False,
                "message": f"处理介绍完成事件失败: {str(e)}"
            }

    def exit_state(self, context: Dict[str, Any]):
        """退出INTRODUCTION状态"""
        self.logger.info("系统退出欢迎和任务介绍状态")
        self.introduction_start_time = None
        self.user_lost_start_time = None
