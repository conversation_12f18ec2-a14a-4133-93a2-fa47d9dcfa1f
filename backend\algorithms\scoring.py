"""
智能康复系统 - 动作评分算法
实现康复动作的质量评估和计分
"""
import numpy as np
import logging
import time
from typing import Dict, List, Optional, Any
from models.data_models import ActionType, TrainingSide
# pose_analyzer 将在需要时延迟导入以避免循环导入
# 根据识别结果进行动作的打分功能
class ActionScorer:
    """动作评分器"""
    
    def __init__(self):
        """初始化动作评分器"""
        self.logger = logging.getLogger(__name__)
        
        # 评分权重配置
        self.scoring_weights = {
            'shoulder_touch': {
                'accuracy': 0.4,      # 准确性（是否触碰到位）
                'stability': 0.3,     # 稳定性（动作平稳度）
                'completion': 0.3     # 完成度（动作完整性）
            },
            'arm_raise': {
                'height': 0.5,        # 高度（抬起高度）
                'stability': 0.3,     # 稳定性
                'smoothness': 0.2     # 平滑度（动作流畅性）
            },
            'finger_touch': {
                'precision': 0.6,     # 精确度（接触精度）
                'sequence': 0.2,      # 序列性（按顺序接触）
                'stability': 0.2      # 稳定性
            },
            'palm_flip': {
                'rotation_angle': 0.5,  # 旋转角度
                'smoothness': 0.3,      # 平滑度
                'stability': 0.2        # 稳定性
            }
        }
        
        # 动作历史记录（用于计算稳定性和平滑度）
        self.action_history: Dict[str, List[Any]] = {}
        self.history_size = 10  # 保留最近10次记录
        
        # 统计信息
        self.stats = {
            'total_scores': 0,
            'average_score': 0.0,
            'score_distribution': {
                'excellent': 0,  # 90-100
                'good': 0,       # 80-89
                'fair': 0,       # 70-79
                'poor': 0        # <70
            },
            'action_scores': {
                'shoulder_touch': [],
                'arm_raise': [],
                'finger_touch': [],
                'palm_flip': []
            }
        }
        
        self.logger.info("动作评分器初始化完成")
    
    def score_action(self, action_type: ActionType, side: TrainingSide, 
                    recognition_result: Dict[str, Any], pose_quality: float) -> Dict[str, Any]:
        """
        对动作进行评分
        
        Args:
            action_type: 动作类型
            side: 训练侧别
            recognition_result: 动作识别结果
            pose_quality: 姿态质量分数
            
        Returns:
            Dict: 评分结果
        """
        try:
            if not recognition_result.get('recognized', False):
                return self._create_score_result(0.0, "动作未识别", {})
            
            # 根据动作类型调用相应的评分函数
            if action_type == 'shoulder_touch':
                score_details = self._score_shoulder_touch(recognition_result, pose_quality)
            elif action_type == 'arm_raise':
                score_details = self._score_arm_raise(recognition_result, pose_quality)
            elif action_type == 'finger_touch':
                score_details = self._score_finger_touch(recognition_result, pose_quality)
            elif action_type == 'palm_flip':
                score_details = self._score_palm_flip(recognition_result, pose_quality)
            else:
                return self._create_score_result(0.0, f"不支持的动作类型: {action_type}", {})
            
            # 计算总分
            total_score = self._calculate_total_score(action_type, score_details)
            
            # 更新历史记录
            self._update_action_history(action_type, side, total_score, score_details)
            
            # 更新统计信息
            self._update_stats(action_type, total_score)
            
            return self._create_score_result(
                score=total_score,
                message=self._get_score_message(total_score),
                details=score_details
            )
            
        except Exception as e:
            self.logger.error(f"动作评分失败: {e}")
            return self._create_score_result(0.0, f"评分异常: {str(e)}", {})
    
    def _score_shoulder_touch(self, recognition_result: Dict[str, Any], pose_quality: float) -> Dict[str, Any]:
        """
        摸肩膀动作评分
        
        Args:
            recognition_result: 识别结果
            pose_quality: 姿态质量
            
        Returns:
            Dict: 评分详情
        """
        details = recognition_result.get('details', {})
        
        # 准确性评分（基于距离）
        distance = details.get('distance', float('inf'))
        threshold = details.get('threshold', 50.0)
        accuracy_score = max(0.0, min(100.0, (1.0 - distance / (threshold * 2)) * 100))
        
        # 稳定性评分（基于姿态质量）
        stability_score = pose_quality
        
        # 完成度评分（基于识别结果中的完成度）
        completion_score = details.get('completion', 0.0) * 100
        
        return {
            'accuracy': accuracy_score,
            'stability': stability_score,
            'completion': completion_score,
            'distance': distance,
            'threshold': threshold
        }
    
    def _score_arm_raise(self, recognition_result: Dict[str, Any], pose_quality: float) -> Dict[str, Any]:
        """
        手臂上抬动作评分
        
        Args:
            recognition_result: 识别结果
            pose_quality: 姿态质量
            
        Returns:
            Dict: 评分详情
        """
        details = recognition_result.get('details', {})
        
        # 高度评分（基于角度和高度比）
        arm_angle = details.get('arm_angle', 0.0)
        height_ratio = details.get('height_ratio', 0.0)
        height_score = min(100.0, (arm_angle / 180.0 + max(0.0, height_ratio)) * 50)
        
        # 稳定性评分
        stability_score = pose_quality
        
        # 平滑度评分（基于历史数据）
        smoothness_score = self._calculate_smoothness('arm_raise', arm_angle)
        
        return {
            'height': height_score,
            'stability': stability_score,
            'smoothness': smoothness_score,
            'arm_angle': arm_angle,
            'height_ratio': height_ratio
        }
    
    def _score_finger_touch(self, recognition_result: Dict[str, Any], pose_quality: float) -> Dict[str, Any]:
        """
        对指动作评分
        
        Args:
            recognition_result: 识别结果
            pose_quality: 姿态质量
            
        Returns:
            Dict: 评分详情
        """
        details = recognition_result.get('details', {})
        
        # 精确度评分（基于最小距离）
        min_distance = details.get('min_distance', float('inf'))
        if min_distance == float('inf'):
            precision_score = 0.0
        else:
            precision_score = max(0.0, min(100.0, (1.0 - min_distance / 40.0) * 100))
        
        # 序列性评分（基于接触的手指数量）
        touches = details.get('touches', [])
        touching_count = len([t for t in touches if t.get('touching', False)])
        sequence_score = min(100.0, touching_count * 25.0)  # 每个手指25分
        
        # 稳定性评分
        stability_score = pose_quality * 0.8  # 手部动作稳定性要求稍低
        
        return {
            'precision': precision_score,
            'sequence': sequence_score,
            'stability': stability_score,
            'min_distance': min_distance,
            'touching_count': touching_count
        }
    
    def _score_palm_flip(self, recognition_result: Dict[str, Any], pose_quality: float) -> Dict[str, Any]:
        """
        手掌翻转动作评分
        
        Args:
            recognition_result: 识别结果
            pose_quality: 姿态质量
            
        Returns:
            Dict: 评分详情
        """
        details = recognition_result.get('details', {})
        
        # 旋转角度评分（简化处理）
        completion = details.get('completion', 0.0)
        rotation_score = completion * 100
        
        # 平滑度评分
        smoothness_score = self._calculate_smoothness('palm_flip', completion)
        
        # 稳定性评分
        stability_score = pose_quality * 0.9  # 手部动作稳定性要求稍低
        
        return {
            'rotation_angle': rotation_score,
            'smoothness': smoothness_score,
            'stability': stability_score,
            'completion': completion
        }
    
    def _calculate_total_score(self, action_type: ActionType, score_details: Dict[str, Any]) -> float:
        """
        计算总分
        
        Args:
            action_type: 动作类型
            score_details: 评分详情
            
        Returns:
            float: 总分 (0-100)
        """
        try:
            weights = self.scoring_weights.get(action_type, {})
            total_score = 0.0
            
            for component, weight in weights.items():
                component_score = score_details.get(component, 0.0)
                total_score += component_score * weight
            
            return min(100.0, max(0.0, total_score))
            
        except Exception as e:
            self.logger.error(f"总分计算失败: {e}")
            return 0.0
    
    def _calculate_smoothness(self, action_type: str, current_value: float) -> float:
        """
        计算动作平滑度
        
        Args:
            action_type: 动作类型
            current_value: 当前值
            
        Returns:
            float: 平滑度分数 (0-100)
        """
        try:
            history_key = f"{action_type}_values"
            
            if history_key not in self.action_history:
                self.action_history[history_key] = []
            
            history = self.action_history[history_key]
            history.append(current_value)
            
            # 保持历史记录大小
            if len(history) > self.history_size:
                history.pop(0)
            
            # 计算变化率的标准差（变化越小越平滑）
            if len(history) < 3:
                return 80.0  # 历史数据不足，给予中等分数
            
            changes = [abs(history[i] - history[i-1]) for i in range(1, len(history))]
            std_dev = float(np.std(changes))
            
            # 将标准差转换为平滑度分数
            smoothness_score = max(0.0, min(100.0, 100.0 - std_dev * 10))
            
            return smoothness_score
            
        except Exception as e:
            self.logger.error(f"平滑度计算失败: {e}")
            return 50.0  # 默认中等分数
    
    def _update_action_history(self, action_type: ActionType, side: TrainingSide, 
                              score: float, details: Dict[str, Any]):
        """
        更新动作历史记录
        
        Args:
            action_type: 动作类型
            side: 训练侧别
            score: 分数
            details: 详情
        """
        history_key = f"{action_type}_{side}"
        
        if history_key not in self.action_history:
            self.action_history[history_key] = []
        
        history = self.action_history[history_key]
        history.append({
            'score': score,
            'details': details,
            'timestamp': time.time()
        })
        
        # 保持历史记录大小
        if len(history) > self.history_size:
            history.pop(0)
    
    def _update_stats(self, action_type: ActionType, score: float):
        """
        更新统计信息
        
        Args:
            action_type: 动作类型
            score: 分数
        """
        self.stats['total_scores'] += 1
        
        # 更新平均分
        current_avg = self.stats['average_score']
        total_count = self.stats['total_scores']
        self.stats['average_score'] = (current_avg * (total_count - 1) + score) / total_count
        
        # 更新分数分布
        if score >= 90:
            self.stats['score_distribution']['excellent'] += 1
        elif score >= 80:
            self.stats['score_distribution']['good'] += 1
        elif score >= 70:
            self.stats['score_distribution']['fair'] += 1
        else:
            self.stats['score_distribution']['poor'] += 1
        
        # 更新动作分数记录
        if action_type in self.stats['action_scores']:
            self.stats['action_scores'][action_type].append(score)
            
            # 保留最近20个分数
            if len(self.stats['action_scores'][action_type]) > 20:
                self.stats['action_scores'][action_type].pop(0)
    
    def _get_score_message(self, score: float) -> str:
        """
        获取分数评价消息
        
        Args:
            score: 分数
            
        Returns:
            str: 评价消息
        """
        if score >= 90:
            return "优秀！动作完成得非常好"
        elif score >= 80:
            return "良好！动作基本到位"
        elif score >= 70:
            return "及格！还有改进空间"
        elif score >= 60:
            return "需要练习！动作不够标准"
        else:
            return "需要重新练习！动作有明显问题"
    
    def _create_score_result(self, score: float, message: str, details: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建评分结果
        
        Args:
            score: 分数
            message: 消息
            details: 详情
            
        Returns:
            Dict: 评分结果
        """
        return {
            'score': score,
            'message': message,
            'details': details,
            'grade': self._get_grade(score),
            'timestamp': time.time()
        }
    
    def _get_grade(self, score: float) -> str:
        """
        获取等级
        
        Args:
            score: 分数
            
        Returns:
            str: 等级
        """
        if score >= 90:
            return "A"
        elif score >= 80:
            return "B"
        elif score >= 70:
            return "C"
        elif score >= 60:
            return "D"
        else:
            return "F"
    
    def get_action_average_score(self, action_type: ActionType) -> float:
        """
        获取动作平均分
        
        Args:
            action_type: 动作类型
            
        Returns:
            float: 平均分
        """
        scores = self.stats['action_scores'].get(action_type, [])
        return sum(scores) / len(scores) if scores else 0.0
    
    def reset_history(self):
        """重置历史记录"""
        self.action_history.clear()
        self.logger.info("动作评分历史记录已重置")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict: 统计信息
        """
        # 计算各动作平均分
        action_averages = {}
        for action_type, scores in self.stats['action_scores'].items():
            action_averages[action_type] = sum(scores) / len(scores) if scores else 0.0
        
        return {
            **self.stats,
            'action_averages': action_averages,
            'history_size': len(self.action_history)
        }

# 全局动作评分器实例
action_scorer = ActionScorer()
