<template>
  <el-card shadow="never" class="task-list-card">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <el-icon class="text-lg text-blue-500 mr-2">
            <List />
          </el-icon>
          <span class="font-semibold text-gray-700">训练任务</span>
        </div>
        <el-tag v-if="actions.length > 0" size="small" type="info">
          {{ completedCount }}/{{ actions.length }}
        </el-tag>
      </div>
    </template>

    <div v-if="actions.length === 0" class="text-center py-8 text-gray-500">
      <el-icon class="text-4xl mb-2">
        <DocumentRemove />
      </el-icon>
      <p>暂无训练任务</p>
      <p class="text-sm mt-1">请等待系统分配任务</p>
    </div>

    <div v-else class="space-y-3">
      <div 
        v-for="(action, index) in actions" 
        :key="`${action.action_type}-${index}`"
        :class="[
          'p-4 rounded-lg border-2 transition-all duration-200',
          isCurrentAction(action)
            ? 'border-blue-500 bg-blue-50 shadow-md transform scale-105'
            : 'border-gray-200'
        ]"
      >
        <!-- 任务头部 -->
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center">
            <el-icon 
              :class="[
                'text-xl mr-2',
                isCurrentAction(action) ? 'text-blue-600' : 'text-gray-500'
              ]"
            >
              <component :is="getActionIcon(action.action_type)" />
            </el-icon>
            <span 
              :class="[
                'font-medium text-lg',
                isCurrentAction(action) ? 'text-blue-700' : 'text-gray-700'
              ]"
            >
              {{ getActionDisplayName(action.action_type) }}
            </span>
          </div>
          <el-tag 
            :type="getActionStatusType(action)"
            size="small"
            effect="plain"
          >
            {{ getActionStatusText(action) }}
          </el-tag>
        </div>

        <!-- 任务详情 -->
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div class="flex items-center">
            <el-icon class="text-gray-400 mr-1">
              <Timer />
            </el-icon>
            <span class="text-gray-600">
              {{ action.sets || 0 }}组 × {{ action.reps_per_set || 0 }}次
            </span>
          </div>
          <div class="flex items-center">
            <el-icon class="text-gray-400 mr-1">
              <TrendCharts />
            </el-icon>
            <span class="text-gray-600">
              难度：{{ getDifficultyText(action.difficulty) }}
            </span>
          </div>
        </div>

        <!-- 进度条（仅当前任务显示） -->
        <div v-if="isCurrentAction(action) && showProgress(action)" class="mt-3">
          <div class="flex justify-between text-xs text-gray-600 mb-1">
            <span>进度</span>
            <span>{{ getCurrentProgress(action) }}%</span>
          </div>
          <el-progress 
            :percentage="getCurrentProgress(action)" 
            :stroke-width="6"
            :show-text="false"
            color="#3b82f6"
          />
        </div>

        <!-- 任务描述（可选） -->
        <div v-if="action.description" class="mt-3 text-sm text-gray-500">
          {{ action.description }}
        </div>
      </div>
    </div>

    <!-- 底部统计信息 -->
    <div v-if="actions.length > 0" class="mt-6 pt-4 border-t border-gray-200">
      <div class="grid grid-cols-3 gap-4 text-center text-sm">
        <div>
          <div class="text-lg font-semibold text-blue-600">{{ completedCount }}</div>
          <div class="text-gray-500">已完成</div>
        </div>
        <div>
          <div class="text-lg font-semibold text-orange-600">{{ inProgressCount }}</div>
          <div class="text-gray-500">进行中</div>
        </div>
        <div>
          <div class="text-lg font-semibold text-gray-600">{{ pendingCount }}</div>
          <div class="text-gray-500">待完成</div>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import {
  List,
  DocumentRemove,
  Timer,
  TrendCharts,
  Trophy,
  User,
  Pointer,
  Refresh
} from '@element-plus/icons-vue'

// Props定义
const props = defineProps({
  actions: { 
    type: Array, 
    default: () => [],
    validator: (value) => Array.isArray(value)
  },
  currentAction: {
    type: Object,
    default: null
  }
})

// 无需事件定义，纯展示组件

// 计算属性
const completedCount = computed(() => {
  return props.actions.filter(action => action.status === 'completed').length
})

const inProgressCount = computed(() => {
  return props.actions.filter(action => action.status === 'in_progress').length
})

const pendingCount = computed(() => {
  return props.actions.filter(action => !action.status || action.status === 'pending').length
})

// 方法
const isCurrentAction = (action) => {
  return props.currentAction && 
         props.currentAction.action_type === action.action_type
}

const getActionStatusType = (action) => {
  if (isCurrentAction(action)) return 'primary'
  if (action.status === 'completed') return 'success'
  if (action.status === 'in_progress') return 'warning'
  return 'info'
}

const getActionStatusText = (action) => {
  if (isCurrentAction(action)) return '进行中'
  if (action.status === 'completed') return '已完成'
  if (action.status === 'in_progress') return '进行中'
  return '待完成'
}

const getActionDisplayName = (actionType) => {
  const nameMap = {
    'shoulder_touch': '肩部触摸',
    'arm_raise': '手臂举起',
    'finger_touch': '手指触摸',
    'palm_flip': '手掌翻转'
  }
  return nameMap[actionType] || actionType
}

const getActionIcon = (actionType) => {
  const iconMap = {
    'shoulder_touch': Trophy,
    'arm_raise': User,
    'finger_touch': Pointer,
    'palm_flip': Refresh
  }
  return iconMap[actionType] || Trophy
}

const getDifficultyText = (difficulty) => {
  if (typeof difficulty === 'number') {
    if (difficulty <= 3) return '简单'
    if (difficulty <= 6) return '中等'
    return '困难'
  }
  return difficulty || '标准'
}

const showProgress = (action) => {
  return action.current_set > 0 || action.current_rep > 0
}

const getCurrentProgress = (action) => {
  if (!action.sets || !action.reps_per_set) return 0
  
  const totalReps = action.sets * action.reps_per_set
  const currentReps = ((action.current_set || 1) - 1) * action.reps_per_set + (action.current_rep || 0)
  
  return Math.min(100, Math.round((currentReps / totalReps) * 100))
}

// 无需点击处理，纯展示组件
</script>

<style scoped>
.task-list-card {
  height: 100%;
  overflow-y: auto;
}

.task-list-card :deep(.el-card__body) {
  padding: 1rem;
}

/* 自定义滚动条 */
.task-list-card::-webkit-scrollbar {
  width: 6px;
}

.task-list-card::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.task-list-card::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.task-list-card::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 任务项悬停效果 */
.task-item:hover {
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 640px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .grid-cols-3 {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}
</style>
