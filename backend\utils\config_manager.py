"""
智能康复系统 - 配置管理工具
提供统一的配置管理功能
"""
import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """初始化配置管理器"""
        self.logger = logging.getLogger(__name__)
        
        # 配置文件路径
        self.config_file = config_file or self._get_default_config_file()
        
        # 默认配置
        self.default_config = self._get_default_config()
        
        # 当前配置
        self.config = self._load_config()
        
        self.logger.info(f"配置管理器初始化完成: {self.config_file}")
    
    def _get_default_config_file(self) -> str:
        """获取默认配置文件路径"""
        return str(Path(__file__).parent.parent / "config" / "system_config.json")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 服务器配置
            "server": {
                "host": "0.0.0.0",
                "port": 5000,
                "debug": False
            },
            
            # 日志配置
            "logging": {
                "level": "INFO",
                "file_enabled": True,
                "console_enabled": True,
                "max_file_size": "10MB",
                "backup_count": 5,
                "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "date_format": "%Y-%m-%d %H:%M:%S"
            },
            
            # ZMQ配置
            "zmq": {
                "detect_port": 6070,
                "camera_port": 6080,
                "timeout": 5000,
                "retry_count": 3
            },
            
            # 视频流配置
            "video_stream": {
                "enabled": True,
                "quality": 80,
                "max_fps": 30,
                "buffer_size": 10
            },
            
            # WebSocket配置
            "websocket": {
                "enabled": True,
                "cors_origins": "*",
                "ping_timeout": 60,
                "ping_interval": 25
            },
            
            # 业务逻辑配置
            "business": {
                "user_detection_threshold": 3,
                "action_recognition_threshold": 0.8,
                "min_score_threshold": 60,
                "preparation_duration": 3.0
            },
            
            # 数据路径配置
            "data_paths": {
                "users_file": "data/users.json",
                "tasks_file": "data/tasks_template.json",
                "logs_dir": "logs",
                "temp_dir": "temp"
            }
        }
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            # 从默认配置开始
            config = self.default_config.copy()
            
            # 如果配置文件存在，加载并合并
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    config = self._merge_config(config, file_config)
                self.logger.info(f"配置文件加载成功: {self.config_file}")
            else:
                self.logger.info("配置文件不存在，使用默认配置")
            
            # 从环境变量覆盖配置
            config = self._load_env_config(config)
            
            return config
            
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            return self.default_config.copy()
    
    def _merge_config(self, base_config: Dict[str, Any], override_config: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置"""
        result = base_config.copy()
        
        for key, value in override_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _load_env_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """从环境变量加载配置"""
        # 定义环境变量映射
        env_mappings = {
            'REHAB_HOST': ['server', 'host'],
            'REHAB_PORT': ['server', 'port'],
            'REHAB_DEBUG': ['server', 'debug'],
            'REHAB_LOG_LEVEL': ['logging', 'level'],
            'REHAB_ZMQ_DETECT_PORT': ['zmq', 'detect_port'],
            'REHAB_ZMQ_CAMERA_PORT': ['zmq', 'camera_port'],
            'REHAB_VIDEO_ENABLED': ['video_stream', 'enabled'],
            'REHAB_WEBSOCKET_ENABLED': ['websocket', 'enabled']
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # 类型转换
                if config_path[-1] in ['port', 'timeout', 'retry_count', 'max_fps', 'buffer_size']:
                    env_value = int(env_value)
                elif config_path[-1] in ['debug', 'enabled']:
                    env_value = env_value.lower() in ('true', '1', 'yes', 'on')
                elif config_path[-1] in ['preparation_duration']:
                    env_value = float(env_value)
                
                # 设置配置值
                current = config
                for key in config_path[:-1]:
                    current = current[key]
                current[config_path[-1]] = env_value
        
        return config
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，如 'server.host'
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key_path.split('.')
            value = self.config
            
            for key in keys:
                value = value[key]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """
        设置配置值
        
        Args:
            key_path: 配置键路径，如 'server.host'
            value: 配置值
        """
        try:
            keys = key_path.split('.')
            current = self.config
            
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            current[keys[-1]] = value
            
        except Exception as e:
            self.logger.error(f"设置配置失败: {e}")
    
    def save_config(self, file_path: Optional[str] = None):
        """保存配置到文件"""
        try:
            save_path = file_path or self.config_file
            
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置已保存到: {save_path}")
            
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
    
    def reload_config(self):
        """重新加载配置"""
        self.config = self._load_config()
        self.logger.info("配置已重新加载")
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self.config.copy()
    
    def validate_config(self) -> bool:
        """验证配置有效性"""
        try:
            # 检查必需的配置项
            required_keys = [
                'server.host',
                'server.port',
                'zmq.detect_port',
                'zmq.camera_port'
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    self.logger.error(f"缺少必需的配置项: {key}")
                    return False
            
            # 检查端口范围
            ports = [
                self.get('server.port'),
                self.get('zmq.detect_port'),
                self.get('zmq.camera_port')
            ]
            
            for port in ports:
                if not (1 <= port <= 65535):
                    self.logger.error(f"端口号无效: {port}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False

# 全局配置管理器实例
config_manager = ConfigManager()
